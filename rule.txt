# Java 条件判断规范

本文档规范项目中 Java 代码的条件判断（if/else）写法，确保代码健壮、避免空指针（NPE）并保持一致性。

## 一、核心原则
1. 代码要像说人话一样清晰易懂
2. 防御性编程，永远不要相信外部输入
3. 保持代码的简洁性和可维护性
4. 避免魔法数字和硬编码
5. 注释要解释为什么，而不是做什么

## 二、实战示例

### 1. 字符串处理
```java
// ✅ 推荐：一行搞定非空判断
if (StringUtils.isNotBlank(userName)) {
    // 处理用户名
}

// ❌ 避免：冗长且容易漏判
if (userName != null && !userName.isEmpty()) {
    // ...
}
```

### 2. 数值比较
```java
// ✅ 推荐：安全比较，避免NPE
if (Objects.equals(status, 1)) {
    // 处理状态
}

// ❌ 危险：可能引发空指针
if (status == 1) {
    // ...
}
```

### 3. 集合处理
```java
// ✅ 推荐：优雅处理空集合
if (CollectionUtils.isNotEmpty(orderList)) {
    orderList.forEach(this::processOrder);
}

// ❌ 避免：老式写法
if (orderList != null && !orderList.isEmpty()) {
    // ...
}
```

### 4. 业务逻辑优化
```java
// ✅ 推荐：卫语句提前返回
public void processOrder(Order order) {
    // 1. 参数校验
    if (order == null) {
        throw new BusinessException("订单不能为空");
    }
    
    // 2. 前置条件检查
    if (order.isCanceled()) {
        log.warn("订单已取消，跳过处理: {}", order.getId());
        return;
    }
    
    // 3. 主业务逻辑
    doProcess(order);
}
```

### 5. 实用小贴士
1. 使用 Optional 优雅处理可能为null的值
2. 保持方法短小精悍（建议不超过20行）
3. 为复杂条件命名，提高可读性
   ```java
   boolean isEligibleForDiscount = user.isVip() 
       && order.getAmount().compareTo(MIN_AMOUNT) > 0 
       && !order.isDiscounted();
       
   if (isEligibleForDiscount) {
       // ...
   }
   ```

## 三、代码审查清单
- [ ] 是否处理了null值？
- [ ] 条件判断是否清晰易懂？
- [ ] 是否有重复的条件判断？
- [ ] 是否考虑了边界条件？
- [ ] 日志记录是否完善？

## 四、常见陷阱
1. 字符串比较要用 `equals()` 而不是 `==`
2. `BigDecimal` 比较要用 `compareTo()` 而不是 `equals()`
3. 避免在循环中频繁创建对象
4. 注意 `Long` 和 `long` 的自动拆箱问题

## 五、性能优化
1. 将不变的条件移到循环外部
2. 使用 `switch` 替代多重 `if-else`
3. 对于频繁调用的方法，考虑使用缓存

## 1. 字符串判断
- 使用 `org.apache.commons.lang3.StringUtils`
  - 非空：`StringUtils.isNotBlank(str)`
  - 空：`StringUtils.isBlank(str)`
  - 避免：`str != null && !str.isEmpty()`（不够健壮）

## 2. 数值/包装类判断
- 相等判断：`Objects.equals(a, b)`（避免 `==` 与 NPE）
- 空判断：
  - `Objects.nonNull(x)`
  - `Objects.isNull(x)`
  - 避免：`x != null`（直接使用 Objects 方法更清晰）

## 3. 集合/Map 判断
- 使用 `org.apache.commons.collections4.CollectionUtils`/`MapUtils`
  - 非空：`CollectionUtils.isNotEmpty(coll)` / `MapUtils.isNotEmpty(map)`
  - 空：`CollectionUtils.isEmpty(coll)` / `MapUtils.isEmpty(map)`
  - 避免：`list != null && !list.isEmpty()`（冗长）

## 4. 数组判断
- 使用 `org.apache.commons.lang3.ArrayUtils`
  - 非空：`ArrayUtils.isNotEmpty(arr)`
  - 避免：`arr != null && arr.length > 0`（不够直观）

## 5. BigDecimal 比较
- 使用 `compareTo()` 进行大小比较：
  ```java
  if (a.compareTo(b) > 0)  // a > b
  if (a.compareTo(b) < 0)  // a < b
  if (a.compareTo(b) == 0) // a = b
  ```
- 避免：`a.equals(b)` 或 `a == b`（精度问题）

## 6. 布尔值判断
- 包装类：`Boolean.TRUE.equals(flag)` / `Boolean.FALSE.equals(flag)`
- 基本类型：直接 `if (isActive)`
- 避免：`flag == true` 或 `flag == Boolean.TRUE`

## 7. 日期/时间判断
- 空判断：`Objects.nonNull(date)`
- 字符串日期：先检查 `StringUtils.isNotBlank(strDate)` 再解析

## 8. 异常与日志
- 捕获异常时提供有意义的上下文信息
- 避免吞没异常（catch 块留空）
- 日志中记录关键变量值

## 9. 事务与业务规则
- 保持方法级 `@Transactional` 注解
- 业务校验不通过时抛出明确的业务异常
- 使用 Guard Clauses 提前返回减少嵌套

## 示例代码
```java
// 字符串
if (StringUtils.isNotBlank(user.getName())) {
    // ...
}

// 数值比较
if (Objects.equals(order.getStatus(), OrderStatus.PAID.getCode())) {
    // ...
}

// 集合非空
if (CollectionUtils.isNotEmpty(orderItems)) {
    orderItems.forEach(item -> process(item));
}

// 使用 Guard Clauses 减少嵌套
public void updateOrder(Order order) {
    if (order == null) {
        throw new BusinessException("订单不能为空");
    }
    if (Objects.equals(order.getStatus(), OrderStatus.CANCELLED.getCode())) {
        throw new BusinessException("已取消的订单不能修改");
    }
    // 主要业务逻辑...
}
```

## 最佳实践
1. 优先使用工具类方法（如 `StringUtils`、`CollectionUtils`）
2. 避免深层嵌套的 if-else，使用卫语句或策略模式重构
3. 保持条件表达式简单，复杂逻辑提取为方法
4. 为所有可能为 null 的返回值添加空检查
5. 单元测试中覆盖边界条件和空值情况

# ====================================================================================
# 代码注释规范

## 1. Service 接口注释要求
- 所有 Service 接口方法必须添加完整的 JavaDoc 注释
- 注释必须包含：
  - 方法功能描述
  - @param 参数说明
  - @return 返回值说明
  - 业务异常说明（如果有）
- 示例：
  ```java
  /**
   * 根据ID查询飞防服务预定详情
   *
   * @param id 主键ID
   * @return 飞防服务预定信息
   * @throws BusinessException 当记录不存在时抛出
   */
  PlaneBook selectById(Long id);
  ```

## 2. Service 实现类注释
- 实现类方法不需要重复添加注释（除非有特殊实现需要说明）
- 保持方法实现简洁，将复杂逻辑抽取为私有方法

## 3. Mapper 接口注释要求
- 所有 Mapper 方法必须添加完整的 JavaDoc 注释
- 需要特别说明 SQL 执行的特殊情况
- 示例：
  ```java
  /**
   * 分页查询飞防服务预定列表（忽略租户）
   *
   * @param page 分页参数
   * @param book 查询条件
   * @return 分页结果
   */
  @InterceptorIgnore(tenantLine = "true")
  IPage<PlaneBookDto> selectPageIgnoreTenant(@Param("page") Page<PlaneBookDto> page, @Param("q") PlaneBook book);
  ```


# ====================================================================================
# Controller 层编码规范
# ====================================================================================

## 1. API 注释
- **模块定义**：使用 `@Api(tags = "模块名称")` 在类上定义 API 模块，名称应简洁明了。
- **接口说明**：使用 `@ApiOperation(value = "接口功能描述")` 在方法上详细描述接口的用途。

## 2. 代码结构
- **业务分离**：当一个 Controller 处理多个相关业务实体时，使用注释块对不同实体的接口进行分组，提高可读性。
- ✅ **业务分离**：当一个 Controller 处理多个相关业务实体时，使用注释块对不同实体的接口进行分组，提高可读性。
  ```java
  // ====================================================================================
  // =============================== 业务A (Entity A) ===================================
  // ====================================================================================
  // ... 业务A的接口 ...

  // ====================================================================================
  // =============================== 业务B (Entity B) ===================================
  // ====================================================================================
  // ... 业务B的接口 ...
  ```

## 3. 接口命名与定义
- ✅ **命名约定**：接口方法名和路径建议采用 `业务实体 + 操作` 的格式（例如 `serveList`, `/book/save`），保持清晰一致。
- ✅ **HTTP 方法**：明确使用 `@PostMapping`, `@GetMapping`, `@DeleteMapping` 等注解，避免使用通用的 `@RequestMapping`。

## 4. 返回类型
- ✅ **统一返回**：所有接口都应返回统一的响应结构，例如 `AjaxResult`。
- ✅ **分页返回**：分页查询的接口应返回包含分页信息的结构，例如 `TableDataInfo`。

## 5. 错误处理
- ✅ **统一异常处理**：在 CUD (Create, Update, Delete) 操作中，必须使用 `try-catch` 块捕获异常。
- ✅ **日志记录**：在 `catch` 块中，应使用 `logger.error()` 记录详细的错误信息和堆栈，方便问题排查。
- ✅ **友好提示**：向前端返回 `AjaxResult.error(e.getMessage())`，提供清晰的错误提示。
  ```java
  @ApiOperation(value = "新增操作")
  @PostMapping("/save")
  public AjaxResult<Boolean> save(@RequestBody YourEntity entity) {
      try {
          // ... 业务逻辑 ...
          return AjaxResult.success(true);
      } catch (Exception e) {
          logger.error("新增操作失败", e);
          return AjaxResult.error("操作失败: " + e.getMessage());
      }
  }
  ```

## 6. 用户权限
- ✅ **权限校验**：对于需要用户登录才能操作的接口，必须通过 `getLoginUser().getUserId()` 获取用户ID，并将其作为参数传递给 Service 层进行数据权限校验。

# ====================================================================================
# Service / Mapper 层 manage 与 app 注释与命名规范
# ====================================================================================

## 1. 注释风格
- Service 允许在类内使用注释分隔块分组接口：
  ```java
  // ====================================================================================
  // =================================== manage =========================================
  // ====================================================================================
  // ... 租户内管理接口 ...

  // ====================================================================================
  // ===================================== app ==========================================
  // ====================================================================================
  // ... 跨租户/前台接口 ...
  ```

## 2. 命名与拦截规范
- Service 命名约定：
  - 遵循租户线（manage）：`selectPage`、`selectById`、`saveXxx`、`updateXxx`、`removeByIds`
  - 忽略租户（app）：方法名后缀加 `IgnoreTenant`（例如：`selectPageIgnoreTenant`、`selectByIdIgnoreTenant`、`saveXxxIgnoreTenant`）
- Mapper 命名与注解：
  - 忽略租户的方法使用 `@InterceptorIgnore(tenantLine = "true")`
  - 推荐的方法命名：
    - 查询分页：`selectPage` / `selectPageIgnoreTenant`
    - 单体查询：`selectById` / `selectByIdIgnoreTenant`
    - C/U/D：`insertIgnoreTenant`、`updateByIdIgnoreTenant`、`deleteByIdIgnoreTenant`

## 3. 数据边界与安全
- manage：所有读写必须遵循租户隔离（Tenant Line），严禁绕过。
- app：
  - 读取可忽略租户做“全局曝光/跨租户检索”；
  - 写入时必须：
    1) 先跨租户读取关联实体（如 serve），
    2) 将关联实体的 `tenantId` 显式写入待保存实体，
    3) 对“本人数据”类接口使用 `userId` 作为数据边界（只允许本人操作）。
- 比较与校验：
  - 整型/包装类比较使用 `Objects.equals(a, b)`，避免 NPE（示例：`Objects.equals(status, 1)`）。
  - 复杂条件使用卫语句提前返回/抛出业务异常，避免嵌套。

## 4. 示例（摘自现有实现）
- Mapper（`shengyu-business/src/main/java/com/shengyu/business/mapper/BusinessMachineBookMapper.java`）
  ```java
  // manage
  IPage<MachineBookDto> selectPage(Page<MachineBookDto> page, @Param("q") MachineBook book);

  // app（忽略租户）
  @InterceptorIgnore(tenantLine = "true")
  IPage<MachineBookDto> selectPageIgnoreTenant(Page<MachineBookDto> page, @Param("q") MachineBook book);

  @InterceptorIgnore(tenantLine = "true")
  MachineBook selectByIdIgnoreTenant(@Param("id") Long id);

  @InterceptorIgnore(tenantLine = "true")
  int insertIgnoreTenant(@Param("et") MachineBook entity);
  ```
- Service（`shengyu-business/src/main/java/com/shengyu/business/service/impl/MachineBookServiceImpl.java`）
  ```java
  // manage
  public IPage<MachineBookDto> selectPage(Page<MachineBookDto> page, MachineBook book) { ... }

  // app（忽略租户 + 用户边界 + 显式租户写入）
  @Transactional
  public boolean saveMachineBookIgnoreTenant(MachineBook machineBook) {
      MachineServe serve = machineServeService.selectByServeTypeIgnoreTenant(machineBook.getServeType());
      if (serve == null || Objects.equals(serve.getStatus(), 0) || Objects.equals(serve.getDeleteFlag(), 1)) {
          throw new CustomException("服务已下架或不存在");
      }
      // 显式设置租户边界
      machineBook.setTenantId(serve.getTenantId());
      return baseMapper.insertIgnoreTenant(machineBook) > 0;
  }
  ```

## 5. Service/Mapper 方法排序规范
- **业务分离排序**：manage 方法在前，app 方法在后
- **功能排序**：每个业务块内按照 CRUD 顺序排列
  - 查询方法（select）
  - 新增方法（save/insert）
  - 修改方法（update）
  - 删除方法（remove/delete）
- **示例排序**：
  ```java
  // manage 块
  selectPage()           // 查询
  selectById()          // 查询
  savePlaneBook()       // 新增
  updatePlaneBook()     // 修改
  removeByIds()         // 删除
  
  // app 块
  selectPageIgnoreTenant()     // 查询
  selectByIdIgnoreTenant()     // 查询
  savePlaneBookIgnoreTenant()  // 新增
  updateByIdIgnoreTenant()     // 修改
  removeByIdIgnoreTenant()     // 删除
  ```

## 6. 代码审查清单（Service/Mapper）
- [ ] 是否正确区分 manage 与 app 方法，并采用统一命名？
- [ ] 忽略租户的方法是否都加了 `@InterceptorIgnore(tenantLine = "true")`？
- [ ] app 写入是否显式设置了 `tenantId`（来源于关联实体）？
- [ ] 涉及个人数据的 app 接口是否基于 `userId` 做了归属校验？
- [ ] 整型/包装类比较是否使用 `Objects.equals`？
- [ ] 异常信息是否清晰，是否使用卫语句减少嵌套？
- [ ] 方法排序是否符合规范（manage在前，app在后，每块内按CRUD排序）？
