# ====================================================================================
# 忽略租户ID（noTenantId）开发规范
# ====================================================================================

## 核心原则
在多租户架构下，部分业务场景需要绕过默认的租户ID过滤，进行跨租户的数据操作。为确保操作的安全性和规范性，特制定本规范。

## 实现步骤

### 1. Mapper 层
- **方法命名**：所有忽略租户ID的数据库操作方法，其名称必须以后缀 `IgnoreTenant` 结尾（例如 `selectPageIgnoreTenant`, `deleteByIdIgnoreTenant`）。
- **注解**：必须在方法上添加 `@InterceptorIgnore(tenantLine = "true")` 注解，以告知 Mybatis-Plus 插件忽略租户ID的自动拼接。
- **XML 实现**：在对应的 Mapper XML 文件中，SQL语句不应包含 `tenant_id` 的查询条件，除非是需要手动指定的场景。

**示例 (`BusinessCustodyBookMapper.java`)**
```java
public interface BusinessCustodyBookMapper extends BaseMapper<CustodyBook> {
    /**
     * 分页查询 - 忽略租户
     */
    @InterceptorIgnore(tenantLine = "true")
    IPage<CustodyBookDto> selectPageIgnoreTenant(Page<CustodyBookDto> page, @Param("q") CustodyBook book);

    /**
     * 根据ID删除 - 忽略租户
     */
    @InterceptorIgnore(tenantLine = "true")
    int deleteByIdIgnoreTenant(@Param("id") Long id);
}
```

### 2. Service 层
- **方法命名**：与 Mapper 层保持一致，Service 接口和实现类中的方法名也必须以 `IgnoreTenant` 结尾。
- **逻辑封装**：所有与忽略租户相关的复杂逻辑（例如，先跨租户查询，再手动赋值租户ID）都应封装在 Service 层，Controller 层不应处理这些细节。
- **手动赋值 `tenantId`**：在需要创建或更新数据的场景下，必须先通过 `IgnoreTenant` 方法从关联的实体（例如 `CustodyServe`）中查询出正确的 `tenantId`，然后手动赋值给当前操作的实体。

**示例 (`CustodyBookServiceImpl.java`)**
```java
@Override
@Transactional
public boolean saveCustodyBook(CustodyBook custodyBook) {
    // 1. 使用 ignoreTenant 方法跨租户获取关联实体
    CustodyServeDto serve = custodyServeService.selectByIdIgnoreTenant(custodyBook.getCustodyId());
    if (serve == null) {
        throw new CustomException("关联的服务不存在");
    }

    // 2. 手动将查询到的 tenantId 赋值给当前实体
    if (serve.getTenantId() != null) {
        custodyBook.setTenantId(serve.getTenantId());
    }

    // 3. 调用 Mapper 中忽略租户的插入方法
    return baseMapper.insertWithServeTenantId(custodyBook) > 0;
}
```

### 3. Controller 层
- **职责分离**：Controller 层只负责接收前端请求和调用 Service 层的方法，不应关心租户ID的具体处理逻辑。
- **方法调用**：Controller 层必须调用 Service 层中明确命名的 `...IgnoreTenant` 方法。
- **用户校验**：在需要进行用户权限校验的场景下（例如，只能删除自己的预定），应调用 Service 层中封装好的 `...WithUserCheck` 方法，该方法内部会使用 `...IgnoreTenant` 来实现跨租户的权限检查。

**示例 (`CustodyServeController.java`)**
```java
@ApiOperation(value = "托管服务--列表")
@PostMapping("/serve/list")
public TableDataInfo<CustodyServeDto> serveList(CustodyServeDto custodyServeDto) {
    // 直接调用 Service 层的 ignoreTenant 方法
    Page<CustodyServeDto> page = buildPage();
    return getDataTable(custodyServeService.selectPageIgnoreTenant(page, custodyServeDto));
}

@ApiOperation(value = "托管预定--删除")
@DeleteMapping("/book/delete")
public AjaxResult<Boolean> bookDelete(@RequestParam Long id) {
    Long loginUserId = getLoginUser().getUserId();
    // 调用封装了权限校验和 ignoreTenant 逻辑的 Service 方法
    return AjaxResult.success(custodyBookService.removeByIdWithUserCheck(id, loginUserId));
}
```

## 总结
通过遵循 `Mapper -> Service -> Controller` 的层级规范，我们可以将忽略租户的逻辑清晰地封装在底层，使上层代码更加简洁、安全、易于维护。
