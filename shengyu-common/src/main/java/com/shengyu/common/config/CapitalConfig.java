package com.shengyu.common.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 读取项目相关配置
 *
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "shengyu")
public class CapitalConfig {
    /**
     * 项目名称
     */
    private String name;

    /**
     * 版本
     */
    private String version;

    /**
     * 版权年份
     */
    private String copyrightYear;

    /**
     * 实例演示开关
     */
    private static boolean demoEnabled;

    /**
     * 上传路径
     */
    private static String profile;

    /**
     * 获取地址开关
     */
    private static boolean addressEnabled;



    /**
     * 知识库中word生成的HTML地址
     */
    private static String learningHtmlPath;

    private static String smsProxyUrl;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getCopyrightYear() {
        return copyrightYear;
    }

    public void setCopyrightYear(String copyrightYear) {
        this.copyrightYear = copyrightYear;
    }

    public static boolean isDemoEnabled() {
        return demoEnabled;
    }

    public void setDemoEnabled(boolean demoEnabled) {
        this.demoEnabled = demoEnabled;
    }

    public static String getLearningHtmlPath() {
        return learningHtmlPath;
    }

    public void setLearningHtmlPath(String learningHtmlPath) {
        CapitalConfig.learningHtmlPath = learningHtmlPath;
    }

    public static String getProfile() {
        return profile;
    }

    public void setProfile(String profile) {
        CapitalConfig.profile = profile;
    }

    public static boolean isAddressEnabled() {
        return addressEnabled;
    }

    public void setAddressEnabled(boolean addressEnabled) {
        CapitalConfig.addressEnabled = addressEnabled;
    }



    public static String getSmsProxyUrl() {
        return smsProxyUrl;
    }

    public void setSmsProxyUrl(String smsProxyUrl) {
        CapitalConfig.smsProxyUrl = smsProxyUrl;
    }

    /**
     * 获取头像上传路径
     */
    public static String getAvatarPath() {
        return getProfile() + "/avatar";
    }

    /**
     * 获取业务文件存储路径
     *
     * @return 业务文件存储路径，基于配置的上传路径加上"/business/"子目录
     */
    public static String getBusinessPath() {
        return getProfile() + "/business/";
    }

    public static String getSupplyDemandPath() {
        return getProfile() + "/supplyDemand/";
    }

    /**
     * 获取下载路径
     */
    public static String getDownloadPath() {
        return getProfile() + "/download/";
    }

    /**
     * 获取上传路径
     */
    public static String getUploadPath() {
        return getProfile() + "/upload";
    }

    /**
     * 获取检查相关资源上传路径
     */
    public static String getPatrolPath() {
        return getProfile() + "/patrol";
    }
}
