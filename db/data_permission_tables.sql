-- 数据权限相关表结构

-- 1. 数据权限配置表
CREATE TABLE `sys_data_scope` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `scope_name` varchar(100) NOT NULL COMMENT '权限名称',
  `scope_type` varchar(20) NOT NULL COMMENT '权限类型(1全部 2自定义 3部门 4部门及子级 5仅本人 6租户 7区域 8客户 9金额限制)',
  `scope_value` varchar(500) DEFAULT NULL COMMENT '权限值',
  `dept_id` bigint(20) DEFAULT NULL COMMENT '部门ID',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
  `area_id` bigint(20) DEFAULT NULL COMMENT '区域ID',
  `customer_id` bigint(20) DEFAULT NULL COMMENT '客户ID',
  `amount_limit` decimal(15,2) DEFAULT NULL COMMENT '金额限制',
  `status` char(1) DEFAULT '1' COMMENT '状态(1正常 0停用)',
  `tenant_id` varchar(20) DEFAULT NULL COMMENT '租户ID',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_scope_type` (`scope_type`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据权限配置表';

-- 2. 角色数据权限关联表
CREATE TABLE `sys_role_data_scope` (
  `role_id` bigint(20) NOT NULL COMMENT '角色ID',
  `data_scope_id` bigint(20) NOT NULL COMMENT '数据权限ID',
  PRIMARY KEY (`role_id`,`data_scope_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色数据权限关联表';

-- 3. 用户数据权限关联表(特殊权限)
CREATE TABLE `sys_user_data_scope` (
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `data_scope_id` bigint(20) NOT NULL COMMENT '数据权限ID',
  PRIMARY KEY (`user_id`,`data_scope_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户数据权限关联表';

-- 4. 用户客户关联表
CREATE TABLE `sys_user_customer` (
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `customer_id` bigint(20) NOT NULL COMMENT '客户ID',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`user_id`,`customer_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户客户关联表';

-- 5. 数据访问日志表
CREATE TABLE `sys_data_access_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `user_name` varchar(50) NOT NULL COMMENT '用户名',
  `table_name` varchar(100) NOT NULL COMMENT '访问表名',
  `operation_type` varchar(20) NOT NULL COMMENT '操作类型(SELECT/INSERT/UPDATE/DELETE)',
  `data_id` varchar(100) DEFAULT NULL COMMENT '数据ID',
  `access_time` datetime NOT NULL COMMENT '访问时间',
  `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `tenant_id` varchar(20) DEFAULT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_access_time` (`access_time`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据访问日志表';

-- 6. 敏感数据脱敏配置表
CREATE TABLE `sys_data_mask_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `table_name` varchar(100) NOT NULL COMMENT '表名',
  `column_name` varchar(100) NOT NULL COMMENT '字段名',
  `mask_type` varchar(20) NOT NULL COMMENT '脱敏类型(ACCOUNT/AMOUNT/PHONE/ID_CARD/EMAIL)',
  `mask_rule` varchar(200) DEFAULT NULL COMMENT '脱敏规则',
  `role_ids` varchar(500) DEFAULT NULL COMMENT '需要脱敏的角色ID(逗号分隔)',
  `status` char(1) DEFAULT '1' COMMENT '状态(1启用 0禁用)',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_table_column` (`table_name`,`column_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='敏感数据脱敏配置表';

-- 插入初始数据权限配置
INSERT INTO `sys_data_scope` VALUES 
(1, '全部数据权限', '1', NULL, NULL, NULL, NULL, NULL, NULL, '1', NULL, 'admin', NOW(), 'admin', NOW(), '管理员全部数据权限'),
(2, '部门数据权限', '3', NULL, NULL, NULL, NULL, NULL, NULL, '1', NULL, 'admin', NOW(), 'admin', NOW(), '本部门数据权限'),
(3, '个人数据权限', '5', NULL, NULL, NULL, NULL, NULL, NULL, '1', NULL, 'admin', NOW(), 'admin', NOW(), '仅本人数据权限'),
(4, '客户经理权限', '8', NULL, NULL, NULL, NULL, NULL, NULL, '1', NULL, 'admin', NOW(), 'admin', NOW(), '客户经理数据权限'),
(5, '金额限制权限', '9', NULL, NULL, NULL, NULL, NULL, 100000.00, '1', NULL, 'admin', NOW(), 'admin', NOW(), '10万元以下数据权限');

-- 插入脱敏配置
INSERT INTO `sys_data_mask_config` VALUES 
(1, 'custody_book', 'account_no', 'ACCOUNT', NULL, '2,3', '1', NOW(), NOW()),
(2, 'custody_book', 'amount', 'AMOUNT', NULL, '3', '1', NOW(), NOW()),
(3, 'sys_user', 'phonenumber', 'PHONE', NULL, '2,3', '1', NOW(), NOW()),
(4, 'sys_user', 'id_card', 'ID_CARD', NULL, '2,3', '1', NOW(), NOW());