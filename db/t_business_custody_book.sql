/*
 Navicat Premium Dump SQL

 Source Server         : localhost_3306
 Source Server Type    : MySQL
 Source Server Version : 50737 (5.7.37)
 Source Host           : localhost:33606
 Source Schema         : am

 Target Server Type    : MySQL
 Target Server Version : 50737 (5.7.37)
 File Encoding         : 65001

 Date: 02/09/2025 17:37:19
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for t_business_custody_book
-- ----------------------------
DROP TABLE IF EXISTS `t_business_custody_book`;
CREATE TABLE `t_business_custody_book`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `book_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '预定编号',
  `book_user_id` bigint(20) NOT NULL COMMENT '预定用户ID',
  `custody_category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'CUSTODY_FULL' COMMENT '托管类型(CUSTODY_FULL/CUSTODY_SINGLE)',
  `custody_id` bigint(20) UNSIGNED NOT NULL COMMENT '托管服务ID',
  `crop_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'corn' COMMENT '作物类型',
  `item_id` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '作业项ID(逗号分隔)',
  `book_status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '预定状态（booking预约中，processing处理中，completed已完成）',
  `unit_price` decimal(10, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '托管单价',
  `unit` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '亩' COMMENT '托管单位（如亩、次）',
  `total_price` decimal(10, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '总金额',
  `contact_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '联系人姓名',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '联系电话',
  `id_card` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '身份证号码',
  `plot_count` decimal(10, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '地块数量',
  `area` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '托管面积(亩)',
  `dept_id` bigint(20) NULL DEFAULT NULL COMMENT '部门ID',
  `address_detail` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '详细地址',
  `service_provider_id` bigint(20) NULL DEFAULT NULL COMMENT '服务商ID',
  `delete_flag` tinyint(1) NOT NULL DEFAULT 0 COMMENT '软删除标识:0-未删除,1-已删除',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tenant_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户ID',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_cb_book_no`(`book_no`) USING BTREE,
  INDEX `idx_cb_custody_type`(`custody_category`) USING BTREE,
  INDEX `idx_cb_custody_id`(`custody_id`) USING BTREE,
  INDEX `idx_cb_book_status`(`book_status`) USING BTREE,
  INDEX `idx_cb_contact_name`(`contact_name`) USING BTREE,
  INDEX `idx_cb_phone`(`phone`) USING BTREE,
  INDEX `idx_cb_deleted_flag`(`delete_flag`) USING BTREE,
  INDEX `idx_cb_tenant_id`(`tenant_id`) USING BTREE,
  INDEX `idx_service_provider_id`(`service_provider_id`) USING BTREE,
  INDEX `idx_cb_dept_id`(`dept_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 13 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '托管预定表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_business_custody_item
-- ----------------------------
DROP TABLE IF EXISTS `t_business_custody_item`;
CREATE TABLE `t_business_custody_item`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '托管名称',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '托管内容',
  `unit_price` decimal(10, 2) NOT NULL COMMENT '托管单价',
  `unit` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '托管单位（如亩、次）',
  `subsidy` decimal(10, 2) NULL DEFAULT NULL COMMENT '补贴金额',
  `discount` decimal(5, 2) NULL DEFAULT NULL COMMENT '折扣率',
  `crop_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '作物类型',
  `delete_flag` tinyint(4) NOT NULL DEFAULT 0 COMMENT '软删除标识: 0-未删除, 1-已删除',
  `create_by` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tenant_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '租户ID',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_cs_name`(`name`) USING BTREE,
  INDEX `idx_cs_crop_type`(`crop_type`) USING BTREE,
  INDEX `idx_cs_deleted_flag`(`delete_flag`) USING BTREE,
  INDEX `idx_cs_tenant_id`(`tenant_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 93 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '点餐式托管表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_business_custody_serve
-- ----------------------------
DROP TABLE IF EXISTS `t_business_custody_serve`;
CREATE TABLE `t_business_custody_serve`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '托管名称',
  `custody_category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'CUSTODY_FULL' COMMENT '托管类型：CUSTODY_FULL全程托管，CUSTODY_SINGLE点餐式托管',
  `service_items` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '托管服务内容（如整地、播种、施肥等）',
  `unit_price` decimal(10, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '托管单价',
  `unit` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '亩' COMMENT '托管单位（如亩、次）',
  `subsidy` decimal(10, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '补贴金额',
  `discount` decimal(5, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '折扣率',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '托管介绍',
  `tags` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标签（逗号分隔）',
  `crop_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'corn' COMMENT '作物类型',
  `image_url` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '图片URL',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '托管状态: 0-下架, 1-上架',
  `service_provider` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '服务商',
  `service_provider_id` bigint(20) NULL DEFAULT NULL COMMENT '服务商ID',
  `dept_id` bigint(20) NULL DEFAULT NULL COMMENT '部门ID',
  `delete_flag` tinyint(1) NOT NULL DEFAULT 0 COMMENT '软删除标识: 0-未删除, 1-已删除',
  `create_by` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tenant_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户ID',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_cf_name`(`name`) USING BTREE,
  INDEX `idx_cf_crop_type`(`crop_type`) USING BTREE,
  INDEX `idx_cf_service_provider`(`service_provider`) USING BTREE,
  INDEX `idx_cf_status`(`status`) USING BTREE,
  INDEX `idx_cf_deleted_flag`(`delete_flag`) USING BTREE,
  INDEX `idx_cf_tenant_id`(`tenant_id`) USING BTREE,
  INDEX `idx_custody_serve_service_provider_id`(`service_provider_id`) USING BTREE,
  INDEX `idx_cf_dept_id`(`dept_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '全程托管表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_business_machine_book
-- ----------------------------
DROP TABLE IF EXISTS `t_business_machine_book`;
CREATE TABLE `t_business_machine_book`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `book_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '预定编号',
  `book_user_id` bigint(20) NOT NULL COMMENT '预定用户',
  `serve_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '服务类型',
  `serve_id` bigint(20) UNSIGNED NOT NULL COMMENT '农机服务ID',
  `crop_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'corn' COMMENT '作物类型',
  `book_status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '预定状态(business_book_status)',
  `contact_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '联系人姓名',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '联系电话',
  `id_card` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '身份证号码',
  `plot_count` decimal(10, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '地块数量',
  `area` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '服务面积(亩)',
  `dept_id` bigint(20) NULL DEFAULT NULL COMMENT '部门ID',
  `address_detail` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '详细地址',
  `expiration_date` datetime NULL DEFAULT NULL COMMENT '有效期限',
  `delete_flag` tinyint(1) NOT NULL DEFAULT 0 COMMENT '软删除标识:0-未删除,1-已删除',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tenant_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户ID',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_mb_book_no`(`book_no`) USING BTREE,
  INDEX `idx_mb_serve_type`(`serve_type`) USING BTREE,
  INDEX `idx_mb_serve_id`(`serve_id`) USING BTREE,
  INDEX `idx_mb_book_status`(`book_status`) USING BTREE,
  INDEX `idx_mb_contact_name`(`contact_name`) USING BTREE,
  INDEX `idx_mb_phone`(`phone`) USING BTREE,
  INDEX `idx_mb_deleted_flag`(`delete_flag`) USING BTREE,
  INDEX `idx_mb_tenant_id`(`tenant_id`) USING BTREE,
  INDEX `idx_mb_dept_id`(`dept_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '农机服务预定表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_business_machine_serve
-- ----------------------------
DROP TABLE IF EXISTS `t_business_machine_serve`;
CREATE TABLE `t_business_machine_serve`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `serve_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '服务编号',
  `serve_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '服务名称',
  `serve_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '服务类型',
  `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '状态（0下架，1下架）',
  `serve_banner` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '服务banner',
  `serve_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '服务描述',
  `dept_id` bigint(20) NULL DEFAULT NULL COMMENT '部门ID',
  `delete_flag` tinyint(4) NOT NULL DEFAULT 0 COMMENT '软删除标识:0-未删除,1-已删除',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tenant_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '租户ID',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_ms_serve_no`(`serve_no`) USING BTREE,
  INDEX `idx_ms_serve_name`(`serve_name`) USING BTREE,
  INDEX `idx_ms_status`(`status`) USING BTREE,
  INDEX `idx_ms_deleted_flag`(`delete_flag`) USING BTREE,
  INDEX `idx_ms_tenant_id`(`tenant_id`) USING BTREE,
  INDEX `idx_ms_dept_id`(`dept_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 25 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '农机服务表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_business_plane_book
-- ----------------------------
DROP TABLE IF EXISTS `t_business_plane_book`;
CREATE TABLE `t_business_plane_book`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `book_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '预定编号',
  `book_user_id` bigint(20) NOT NULL COMMENT '预定用户',
  `serve_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '服务类型',
  `serve_id` bigint(20) UNSIGNED NOT NULL COMMENT '飞防服务ID',
  `book_status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '预定状态(business_book_status)',
  `contact_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '联系人姓名',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '联系电话',
  `id_card` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '身份证号码',
  `plot_count` decimal(10, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '地块数量',
  `area` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '服务面积(亩)',
  `dept_id` bigint(20) NULL DEFAULT NULL COMMENT '部门ID',
  `address_detail` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '详细地址',
  `total_price` decimal(10, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '服务总价',
  `expiration_date` datetime NULL DEFAULT NULL COMMENT '有效期限',
  `delete_flag` tinyint(1) NOT NULL DEFAULT 0 COMMENT '软删除标识:0-未删除,1-已删除',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tenant_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户ID',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_pb_book_no`(`book_no`) USING BTREE,
  INDEX `idx_pb_serve_type`(`serve_type`) USING BTREE,
  INDEX `idx_pb_serve_id`(`serve_id`) USING BTREE,
  INDEX `idx_pb_book_status`(`book_status`) USING BTREE,
  INDEX `idx_pb_contact_name`(`contact_name`) USING BTREE,
  INDEX `idx_pb_phone`(`phone`) USING BTREE,
  INDEX `idx_pb_deleted_flag`(`delete_flag`) USING BTREE,
  INDEX `idx_pb_tenant_id`(`tenant_id`) USING BTREE,
  INDEX `idx_pb_dept_id`(`dept_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '飞防服务预定表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_business_plane_serve
-- ----------------------------
DROP TABLE IF EXISTS `t_business_plane_serve`;
CREATE TABLE `t_business_plane_serve`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `serve_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '服务编号',
  `serve_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '服务名称',
  `serve_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '服务类型',
  `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '状态（0下架，1下架）',
  `serve_banner` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '服务banner',
  `serve_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '服务描述',
  `unit_price` decimal(10, 2) NOT NULL COMMENT '服务单价',
  `dept_id` bigint(20) NULL DEFAULT NULL COMMENT '部门ID',
  `delete_flag` tinyint(4) NOT NULL DEFAULT 0 COMMENT '软删除标识:0-未删除,1-已删除',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tenant_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '租户ID',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_ps_serve_no`(`serve_no`) USING BTREE,
  INDEX `idx_ps_serve_name`(`serve_name`) USING BTREE,
  INDEX `idx_ps_status`(`status`) USING BTREE,
  INDEX `idx_ps_deleted_flag`(`delete_flag`) USING BTREE,
  INDEX `idx_ps_tenant_id`(`tenant_id`) USING BTREE,
  INDEX `idx_ps_dept_id`(`dept_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '飞防服务表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_business_supply_demand
-- ----------------------------
DROP TABLE IF EXISTS `t_business_supply_demand`;
CREATE TABLE `t_business_supply_demand`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `info_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '信息标题',
  `info_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'supply' COMMENT '信息类型 (supply:供应 demand:需求)',
  `serve_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'agricultural_products' COMMENT '服务类型（business_supply_demand_serve_type字典，农资，农机，种子，用工，农产品，农业服务，农村资产，其他）',
  `crop_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'corn' COMMENT '作物类型',
  `contact_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '联系人姓名',
  `phone_number` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '联系电话',
  `id_card` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '身份证号码',
  `image_url` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '图片地址',
  `dept_id` bigint(20) NULL DEFAULT NULL COMMENT '部门ID',
  `address_detail` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '详细地址',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '服务描述',
  `publish_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '发布时间',
  `audit_status` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'AUDITING' COMMENT '审核状态 (business_audit_status字典)',
  `expiration_date` datetime NULL DEFAULT NULL COMMENT '有效期限',
  `reviewer_id` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '审核人ID',
  `review_time` datetime NULL DEFAULT NULL COMMENT '审核时间',
  `tenant_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户ID',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delete_flag` tinyint(1) NOT NULL DEFAULT 0 COMMENT '软删除标志 (0:未删除 1:已删除)',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_scd_info_type`(`info_type`) USING BTREE,
  INDEX `idx_scd_crop_type`(`crop_type`) USING BTREE,
  INDEX `idx_scd_status`(`audit_status`) USING BTREE,
  INDEX `idx_scd_delete`(`delete_flag`) USING BTREE,
  INDEX `idx_scd_dept_id`(`dept_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 12 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '供需信息发布表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
