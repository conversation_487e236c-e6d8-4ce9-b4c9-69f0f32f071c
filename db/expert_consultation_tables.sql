-- 专家问诊系统表结构设计（免费咨询轻量版）
-- 创建时间: 2025-01-27
-- 说明: 包含专家管理、问诊记录等核心表

-- 1. 专家管理表
DROP TABLE IF EXISTS `t_business_consultation_expert`;
CREATE TABLE `t_business_consultation_expert` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '专家主键ID',
  `expert_name` varchar(50) NOT NULL COMMENT '专家姓名',
  `expert_no` varchar(30) NOT NULL COMMENT '专家编号',
  `expert_type` varchar(30) NOT NULL COMMENT '专家类型（breeding-育种专家，cultivation-栽培专家，soil-土壤专家）',
  `title` varchar(100) DEFAULT '' COMMENT '专家职称',
  `organization` varchar(200) DEFAULT '' COMMENT '所属机构',
  `introduction` varchar(1000) DEFAULT '' COMMENT '个人简介',
  `rating` decimal(3,2) DEFAULT 5.00 COMMENT '评分（1-5分）',
  `consultation_count` int(11) DEFAULT 0 COMMENT '问诊次数',
  `is_recommend` char(1) DEFAULT '0' COMMENT '是否推荐（0-否，1-是）',
  `recommend_sort` int(4) DEFAULT 0 COMMENT '推荐排序（数值越大排序越靠前）',
  `status` char(1) DEFAULT '1' COMMENT '专家状态（0-停用，1-启用）',
  `delete_flag` char(1) DEFAULT '0' COMMENT '删除标志（0存在 1删除）',
  `tenant_id` varchar(20) DEFAULT '' COMMENT '租户编号',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注信息',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_consultation_expert_no` (`expert_no`) USING BTREE,
  KEY `idx_consultation_expert_type` (`expert_type`) USING BTREE,
  KEY `idx_consultation_expert_status` (`status`) USING BTREE,
  KEY `idx_consultation_expert_recommend` (`is_recommend`, `recommend_sort`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='专家管理表';

-- 2. 问诊记录表
DROP TABLE IF EXISTS `t_business_consultation_record`;
CREATE TABLE `t_business_consultation_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '问诊ID',
  `consultation_no` varchar(30) NOT NULL COMMENT '问诊编号',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `expert_id` bigint(20) DEFAULT NULL COMMENT '专家ID',
  `expert_type` varchar(30) NOT NULL COMMENT '咨询专家类型',
  `title` varchar(200) NOT NULL COMMENT '问题标题',
  `description` text NOT NULL COMMENT '问题描述',
  `crop_type` varchar(100) DEFAULT '' COMMENT '作物类型',
  `status` varchar(20) DEFAULT 'pending' COMMENT '状态（pending-待回复，replied-已回复，closed-已关闭）',
  `reply_time` datetime DEFAULT NULL COMMENT '回复时间',
  `close_time` datetime DEFAULT NULL COMMENT '关闭时间',
  `solution` text COMMENT '解决方案',
  `satisfaction_rating` int(11) DEFAULT 0 COMMENT '满意度评分（1-5分）',
  `delete_flag` char(1) DEFAULT '0' COMMENT '删除标志（0存在 1删除）',
  `tenant_id` varchar(20) DEFAULT '' COMMENT '租户编号',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注信息',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_consultation_record_no` (`consultation_no`) USING BTREE,
  KEY `idx_consultation_record_user_id` (`user_id`) USING BTREE,
  KEY `idx_consultation_record_expert_id` (`expert_id`) USING BTREE,
  KEY `idx_consultation_record_status` (`status`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='问诊记录表';

-- 3. 问诊回复表
DROP TABLE IF EXISTS `t_business_consultation_reply`;
CREATE TABLE `t_business_consultation_reply` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '回复ID',
  `consultation_id` bigint(20) NOT NULL COMMENT '问诊ID',
  `replier_id` bigint(20) NOT NULL COMMENT '回复人ID',
  `replier_type` char(1) NOT NULL COMMENT '回复人类型（1-用户，2-专家）',
  `content` text COMMENT '回复内容',
  `is_read` char(1) DEFAULT '0' COMMENT '是否已读（0-未读，1-已读）',
  `delete_flag` char(1) DEFAULT '0' COMMENT '删除标志（0存在 1删除）',
  `tenant_id` varchar(20) DEFAULT '' COMMENT '租户编号',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_consultation_reply_consultation_id` (`consultation_id`) USING BTREE,
  KEY `idx_consultation_reply_replier_id` (`replier_id`) USING BTREE,
  KEY `idx_consultation_reply_create_time` (`create_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='问诊回复表';

-- 初始化字典数据
-- 专家类型字典类型
INSERT INTO `sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `remark`) VALUES
('专家类型', 'business_expert_type', '1', 'admin', NOW(), '农业专家类型分类');

-- 专家类型字典数据
INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `remark`) VALUES
(1, '育种专家', 'breeding', 'business_expert_type', '', 'primary', 'N', '1', 'admin', NOW(), '农作物育种专家'),
(2, '栽培专家', 'cultivation', 'business_expert_type', '', 'success', 'N', '1', 'admin', NOW(), '农作物栽培专家'),
(3, '土壤专家', 'soil', 'business_expert_type', '', 'warning', 'N', '1', 'admin', NOW(), '土壤改良专家');

-- 问诊状态字典类型
INSERT INTO `sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `remark`) VALUES
('问诊状态', 'business_consultation_status', '1', 'admin', NOW(), '问诊状态分类');

-- 问诊状态字典数据
INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `remark`) VALUES
(1, '待回复', 'pending', 'business_consultation_status', '', 'warning', 'N', '1', 'admin', NOW(), '等待专家回复'),
(2, '已回复', 'replied', 'business_consultation_status', '', 'success', 'N', '1', 'admin', NOW(), '专家已回复'),
(3, '已关闭', 'closed', 'business_consultation_status', '', 'default', 'N', '1', 'admin', NOW(), '问诊已关闭'); 