# 财务管理系统数据权限设计方案

## 1. 权限模型架构

### 1.1 多维度权限控制
```
用户(User) -> 角色(Role) -> 权限(Permission) -> 数据范围(DataScope)
     |              |              |                    |
     |              |              |                    |
  租户隔离      功能权限        操作权限            数据权限
(TenantId)    (Menu/API)    (CRUD操作)        (数据可见范围)
```

### 1.2 数据权限层级
1. **租户级别** - 完全隔离不同租户数据
2. **组织级别** - 基于部门/机构的数据权限
3. **角色级别** - 基于角色的数据访问控制
4. **用户级别** - 个人数据权限控制
5. **业务级别** - 特定业务场景的数据权限

## 2. 核心组件设计

### 2.1 数据权限枚举
```java
public enum DataScopeType {
    ALL("1", "全部数据权限"),
    CUSTOM("2", "自定义数据权限"), 
    DEPT("3", "本部门数据权限"),
    DEPT_AND_CHILD("4", "本部门及以下数据权限"),
    SELF("5", "仅本人数据权限"),
    TENANT("6", "租户数据权限"),
    AREA("7", "区域数据权限");
}
```

### 2.2 数据权限注解增强
```java
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface DataScope {
    String deptAlias() default "";
    String userAlias() default "";
    String tenantAlias() default "";
    String areaAlias() default "";
    DataScopeType[] scopes() default {};
    boolean ignoreTenant() default false;
}
```

## 3. 财务业务权限设计

### 3.1 财务数据分类
- **资金数据**: 账户余额、交易记录、资金流水
- **客户数据**: 客户信息、客户资产、客户交易
- **产品数据**: 理财产品、投资组合、收益数据
- **风控数据**: 风险评估、合规检查、审计日志

### 3.2 权限控制策略
1. **垂直权限**: 不同层级用户看到不同详细程度的数据
2. **水平权限**: 同级用户只能看到自己负责的客户/产品数据
3. **时间权限**: 基于时间范围的数据访问控制
4. **金额权限**: 基于金额阈值的数据访问控制

## 4. 实现方案

### 4.1 数据权限拦截器
通过MyBatis-Plus的多租户插件和自定义拦截器实现数据权限控制

### 4.2 权限SQL动态生成
基于用户角色和权限配置，动态生成WHERE条件

### 4.3 缓存优化
权限信息缓存，减少数据库查询

### 4.4 审计日志
记录所有数据访问操作，确保合规性

## 5. 安全考虑

### 5.1 数据脱敏
敏感财务数据在展示时进行脱敏处理

### 5.2 访问控制
严格的API访问控制和参数校验

### 5.3 日志监控
完整的操作日志和异常监控

## 6. 配置管理

### 6.1 权限配置表
- sys_data_scope: 数据权限配置
- sys_role_data_scope: 角色数据权限关联
- sys_user_data_scope: 用户特殊数据权限

### 6.2 动态配置
支持运行时动态调整数据权限配置