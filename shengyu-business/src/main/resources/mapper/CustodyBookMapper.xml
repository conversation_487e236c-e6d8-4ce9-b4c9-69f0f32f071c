<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shengyu.business.mapper.BusinessCustodyBookMapper">
    <resultMap id="BaseResultMap" type="com.shengyu.business.domain.CustodyBook">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="bookNo" column="book_no" jdbcType="VARCHAR"/>
        <result property="bookUserId" column="book_user_id" jdbcType="BIGINT"/>
        <result property="custodyCategory" column="custody_category" jdbcType="VARCHAR"/>
        <result property="custodyId" column="custody_id" jdbcType="BIGINT"/>
        <result property="itemId" column="item_id" jdbcType="VARCHAR"/>
        <result property="cropType" column="crop_type" jdbcType="VARCHAR"/>
        <result property="bookStatus" column="book_status" jdbcType="VARCHAR"/>
        <result property="unitPrice" column="unit_price" jdbcType="DECIMAL"/>
        <result property="unit" column="unit" jdbcType="VARCHAR"/>
        <result property="totalPrice" column="total_price" jdbcType="DECIMAL"/>
        <result property="contactName" column="contact_name" jdbcType="VARCHAR"/>
        <result property="phone" column="phone" jdbcType="VARCHAR"/>
        <result property="idCard" column="id_card" jdbcType="VARCHAR"/>
        <result property="plotCount" column="plot_count" jdbcType="DECIMAL"/>
        <result property="area" column="area" jdbcType="DECIMAL"/>
        <result property="deptId" column="dept_id" jdbcType="BIGINT"/>
        <result property="addressDetail" column="address_detail" jdbcType="VARCHAR"/>
        <result property="serviceProviderId" column="service_provider_id" jdbcType="BIGINT"/>
        <result property="deleteFlag" column="delete_flag" jdbcType="TINYINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        cb.id,cb.book_no,cb.book_user_id,cb.custody_category,
        cb.custody_id,cb.item_id,cb.crop_type,cb.book_status,cb.unit_price,cb.unit,cb.total_price,cb.contact_name,
        cb.phone,cb.id_card,cb.plot_count,cb.area,
        cb.dept_id,cb.address_detail,cb.service_provider_id,cb.delete_flag,
        cb.create_time,cb.update_time,cb.tenant_id,
        cb.remark
    </sql>

    <select id="selectPage" resultType="com.shengyu.business.domain.dto.CustodyBookDto">
        SELECT
        <include refid="Base_Column_List"/>,
        cs.name as custodyName,
        dd.dict_label as bookStatusName,
        dd2.dict_label as custodyCategoryName,
        crop_dict.dict_label AS cropTypeName,
        dept.dept_name AS deptName,
        sp.provider_name AS serviceProvider
        FROM t_business_custody_book cb
        LEFT JOIN t_business_custody_serve cs ON cb.custody_id = cs.id
        LEFT JOIN sys_dict_data dd ON dd.dict_value = cb.book_status AND dd.dict_type = 'business_book_status'
        LEFT JOIN sys_dict_data dd2 ON cb.custody_category = dd2.dict_value AND dd2.dict_type = 'business_custody_type'
        LEFT JOIN sys_dict_data crop_dict ON cb.crop_type = crop_dict.dict_value AND crop_dict.dict_type = 'business_crop_type'
        LEFT JOIN sys_dept dept ON dept.dept_id = cb.dept_id
        LEFT JOIN t_mall_service_provider sp ON cb.service_provider_id = sp.id
        WHERE cb.delete_flag = 0
        <if test="q.bookNo != null and q.bookNo != ''">
            AND cb.book_no LIKE CONCAT(#{q.bookNo}, '%')
        </if>
        <if test="q.custodyCategory != null">
            AND cb.custody_category = #{q.custodyCategory.name}
        </if>
        <if test="q.bookStatus != null and q.bookStatus != ''">
            AND cb.book_status = #{q.bookStatus}
        </if>
        <if test="q.cropType != null and q.cropType != ''">
            AND cb.crop_type = #{q.cropType}
        </if>
        <if test="q.contactName != null and q.contactName != ''">
            AND cb.contact_name LIKE CONCAT(#{q.contactName}, '%')
        </if>
        <if test="q.phone != null and q.phone != ''">
            AND cb.phone LIKE CONCAT(#{q.phone}, '%')
        </if>
        <if test="q.idCard != null and q.idCard != ''">
            AND cb.id_card LIKE CONCAT(#{q.idCard}, '%')
        </if>

        <if test="q.addressDetail != null and q.addressDetail != ''">
            AND cb.address_detail LIKE CONCAT('%', #{q.addressDetail}, '%')
        </if>
        <if test="q.bookUserId != null">
            AND cb.book_user_id = #{q.bookUserId}
        </if>
        <if test="q.custodyName != null and q.custodyName != ''">
            AND cs.name LIKE CONCAT(#{q.custodyName}, '%')
        </if>
        <if test="q.plotCount != null">
            AND cb.plot_count = #{q.plotCount}
        </if>
        <if test="q.area != null">
            AND cb.area = #{q.area}
        </if>
        <if test="q.totalPrice != null">
            AND cb.total_price = #{q.totalPrice}
        </if>
    </select>

    <select id="selectCustodyBookById" resultType="com.shengyu.business.domain.dto.CustodyBookDto">
        SELECT
        <include refid="Base_Column_List"/>,
        cs.name as custodyName, dd.dict_label as bookStatusName,
        dd2.dict_label as custodyCategoryName,
        crop_dict.dict_label AS cropTypeName,
        dept.dept_name AS deptName,
        sp.provider_name AS serviceProvider
        FROM t_business_custody_book cb
        LEFT JOIN t_business_custody_serve cs ON cb.custody_id = cs.id
        LEFT JOIN sys_dict_data dd ON dd.dict_value = cb.book_status AND dd.dict_type = 'business_book_status'
        LEFT JOIN sys_dict_data dd2 ON cb.custody_category = dd2.dict_value AND dd2.dict_type = 'business_custody_type'
        LEFT JOIN sys_dict_data crop_dict ON cb.crop_type = crop_dict.dict_value AND crop_dict.dict_type = 'business_crop_type'
        LEFT JOIN sys_dept dept ON dept.dept_id = cb.dept_id
        LEFT JOIN t_mall_service_provider sp ON cb.service_provider_id = sp.id
        WHERE cb.id = #{id}
    </select>

    <select id="selectCustodyBookByIdIgnoreTenant" resultType="com.shengyu.business.domain.dto.CustodyBookDto">
        SELECT
        <include refid="Base_Column_List"/>,
        cs.name as custodyName, dd.dict_label as bookStatusName,
        dd2.dict_label as custodyCategoryName,
        crop_dict.dict_label AS cropTypeName,
        dept.dept_name AS deptName,
        sp.provider_name AS serviceProvider
        FROM t_business_custody_book cb
        LEFT JOIN t_business_custody_serve cs ON cb.custody_id = cs.id
        LEFT JOIN sys_dict_data dd ON dd.dict_value = cb.book_status AND dd.dict_type = 'business_book_status'
        LEFT JOIN sys_dict_data dd2 ON cb.custody_category = dd2.dict_value AND dd2.dict_type = 'business_custody_type'
        LEFT JOIN sys_dict_data crop_dict ON cb.crop_type = crop_dict.dict_value AND crop_dict.dict_type = 'business_crop_type'
        LEFT JOIN sys_dept dept ON dept.dept_id = cb.dept_id
        LEFT JOIN t_mall_service_provider sp ON cb.service_provider_id = sp.id
        WHERE cb.id = #{id}
    </select>
    
    <select id="selectPageIgnoreTenant" resultType="com.shengyu.business.domain.dto.CustodyBookDto">
        SELECT
        <include refid="Base_Column_List"/>,
        cs.name as custodyName,
        dd.dict_label as bookStatusName,
        dd2.dict_label as custodyCategoryName,
        crop_dict.dict_label AS cropTypeName,
        dept.dept_name AS deptName,
        sp.provider_name AS serviceProvider
        FROM t_business_custody_book cb
        LEFT JOIN t_business_custody_serve cs ON cb.custody_id = cs.id
        LEFT JOIN sys_dict_data dd ON dd.dict_value = cb.book_status AND dd.dict_type = 'business_book_status'
        LEFT JOIN sys_dict_data dd2 ON cb.custody_category = dd2.dict_value AND dd2.dict_type = 'business_custody_type'
        LEFT JOIN sys_dict_data crop_dict ON cb.crop_type = crop_dict.dict_value AND crop_dict.dict_type = 'business_crop_type'
        LEFT JOIN sys_dept dept ON dept.dept_id = cb.dept_id
        LEFT JOIN t_mall_service_provider sp ON cb.service_provider_id = sp.id
        WHERE cb.delete_flag = 0
        <if test="q.bookNo != null and q.bookNo != ''">
            AND cb.book_no LIKE CONCAT(#{q.bookNo}, '%')
        </if>
        <if test="q.custodyCategory != null">
            AND cb.custody_category = #{q.custodyCategory.name}
        </if>
        <if test="q.bookStatus != null and q.bookStatus != ''">
            AND cb.book_status = #{q.bookStatus}
        </if>
        <if test="q.cropType != null and q.cropType != ''">
            AND cb.crop_type = #{q.cropType}
        </if>
        <if test="q.contactName != null and q.contactName != ''">
            AND cb.contact_name LIKE CONCAT(#{q.contactName}, '%')
        </if>
        <if test="q.phone != null and q.phone != ''">
            AND cb.phone LIKE CONCAT(#{q.phone}, '%')
        </if>
        <if test="q.idCard != null and q.idCard != ''">
            AND cb.id_card LIKE CONCAT(#{q.idCard}, '%')
        </if>

        <if test="q.addressDetail != null and q.addressDetail != ''">
            AND cb.address_detail LIKE CONCAT('%', #{q.addressDetail}, '%')
        </if>
        <if test="q.bookUserId != null">
            AND cb.book_user_id = #{q.bookUserId}
        </if>
        <if test="q.custodyName != null and q.custodyName != ''">
            AND cs.name LIKE CONCAT(#{q.custodyName}, '%')
        </if>
        <if test="q.plotCount != null">
            AND cb.plot_count = #{q.plotCount}
        </if>
        <if test="q.area != null">
            AND cb.area = #{q.area}
        </if>
        <if test="q.totalPrice != null">
            AND cb.total_price = #{q.totalPrice}
        </if>
    </select>

    <!-- 自定义插入（忽略租户），用于显式设置 tenant_id 的场景 -->
    <insert id="insertWithServeTenantId" parameterType="com.shengyu.business.domain.CustodyBook" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_business_custody_book
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bookNo != null">book_no,</if>
            <if test="bookUserId != null">book_user_id,</if>
            <if test="custodyCategory != null">custody_category,</if>
            <if test="custodyId != null">custody_id,</if>
            <if test="itemId != null">item_id,</if>
            <if test="cropType != null">crop_type,</if>
            <if test="bookStatus != null">book_status,</if>
            <if test="unitPrice != null">unit_price,</if>
            <if test="unit != null">unit,</if>
            <if test="totalPrice != null">total_price,</if>
            <if test="contactName != null">contact_name,</if>
            <if test="phone != null">phone,</if>
            <if test="idCard != null">id_card,</if>
            <if test="plotCount != null">plot_count,</if>
            <if test="area != null">area,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="addressDetail != null">address_detail,</if>
            <if test="serviceProviderId != null">service_provider_id,</if>
            <if test="deleteFlag != null">delete_flag,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="tenantId != null">tenant_id,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="bookNo != null">#{bookNo},</if>
            <if test="bookUserId != null">#{bookUserId},</if>
            <if test="custodyCategory != null">#{custodyCategory},</if>
            <if test="custodyId != null">#{custodyId},</if>
            <if test="itemId != null">#{itemId},</if>
            <if test="cropType != null">#{cropType},</if>
            <if test="bookStatus != null">#{bookStatus},</if>
            <if test="unitPrice != null">#{unitPrice},</if>
            <if test="unit != null">#{unit},</if>
            <if test="totalPrice != null">#{totalPrice},</if>
            <if test="contactName != null">#{contactName},</if>
            <if test="phone != null">#{phone},</if>
            <if test="idCard != null">#{idCard},</if>
            <if test="plotCount != null">#{plotCount},</if>
            <if test="area != null">#{area},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="addressDetail != null">#{addressDetail},</if>
            <if test="serviceProviderId != null">#{serviceProviderId},</if>
            <if test="deleteFlag != null">#{deleteFlag},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="tenantId != null">#{tenantId},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <!-- 自定义更新（忽略租户），用于显式设置/保持 tenant_id 的场景 -->
    <update id="updateByIdWithServeTenantId" parameterType="com.shengyu.business.domain.CustodyBook">
        UPDATE t_business_custody_book
        <set>
            <if test="bookNo != null">book_no = #{bookNo},</if>
            <if test="bookUserId != null">book_user_id = #{bookUserId},</if>
            <if test="custodyCategory != null">custody_category = #{custodyCategory},</if>
            <if test="custodyId != null">custody_id = #{custodyId},</if>
            <if test="itemId != null">item_id = #{itemId},</if>
            <if test="cropType != null">crop_type = #{cropType},</if>
            <if test="bookStatus != null">book_status = #{bookStatus},</if>
            <if test="unitPrice != null">unit_price = #{unitPrice},</if>
            <if test="unit != null">unit = #{unit},</if>
            <if test="totalPrice != null">total_price = #{totalPrice},</if>
            <if test="contactName != null">contact_name = #{contactName},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="idCard != null">id_card = #{idCard},</if>
            <if test="plotCount != null">plot_count = #{plotCount},</if>
            <if test="area != null">area = #{area},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="addressDetail != null">address_detail = #{addressDetail},</if>
            <if test="serviceProviderId != null">service_provider_id = #{serviceProviderId},</if>
            <if test="deleteFlag != null">delete_flag = #{deleteFlag},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
            <if test="remark != null">remark = #{remark},</if>
        </set>
        WHERE id = #{id}
    </update>

    <delete id="deleteByIdIgnoreTenant">
        DELETE FROM t_business_custody_book WHERE id = #{id}
    </delete>

</mapper>
