<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shengyu.business.mapper.ServiceProviderMapper">
    <resultMap type="com.shengyu.business.domain.ServiceProvider" id="ServiceProviderResult">
        <result property="id" column="id"/>
        <result property="providerNo" column="provider_no"/>
        <result property="userId" column="user_id"/>
        <result property="providerName" column="provider_name"/>
        <result property="businessLicense" column="business_license"/>
        <result property="businessLicenseImage" column="business_license_image"/>
        <result property="legalPerson" column="legal_person"/>
        <result property="legalPersonIdCard" column="legal_person_id_card"/>
        <result property="contactPerson" column="contact_person"/>
        <result property="contactPhone" column="contact_phone"/>
        <result property="contactEmail" column="contact_email"/>
        <result property="businessAddress" column="business_address"/>
        <result property="registeredAddress" column="registered_address"/>
        <result property="businessScope" column="business_scope"/>
        <result property="providerType" column="provider_type"/>
        <result property="providerLevel" column="provider_level"/>
        <result property="auditStatus" column="audit_status"/>
        <result property="approvalOpinion" column="approval_opinion"/>
        <result property="approvalTime" column="approval_time"/>
        <result property="approvalBy" column="approval_by"/>
        <result property="serviceArea" column="service_area"/>
        <result property="serviceCategory" column="service_category"/>
        <result property="introduction" column="introduction"/>
        <result property="logo" column="logo"/>
        <result property="score" column="score"/>
        <result property="serviceCount" column="service_count"/>
        <result property="deleteFlag" column="delete_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>

    <sql id="Base_Column_List">
        sp.id,
        sp.provider_no, sp.user_id, sp.provider_name, sp.business_license, sp.business_license_image,
        sp.legal_person, sp.legal_person_id_card, sp.contact_person, sp.contact_phone, sp.contact_email,
        sp.business_address, sp.registered_address, sp.business_scope, sp.provider_type, sp.provider_level,
        sp.audit_status, sp.approval_opinion, sp.approval_time, sp.approval_by, sp.service_area, sp.service_category,
        sp.introduction, sp.logo, sp.score, sp.service_count, sp.delete_flag, sp.create_by, sp.create_time,
        sp.update_by, sp.update_time, sp.remark, sp.tenant_id
    </sql>

    <select id="selectServiceProviderById" parameterType="Long"
            resultType="com.shengyu.business.domain.dto.ServiceProviderDto">
        select
        <include refid="Base_Column_List"/>
        , dd.dict_label as providerTypeName
        , dd2.dict_label as serviceCategoryName
        , dd3.dict_label as providerLevelName
        , dd4.dict_label as auditStatusName
        from t_mall_service_provider sp
        left join sys_dict_data dd on sp.provider_type = dd.dict_value and dd.dict_type = 'mall_service_provider_type'
        left join sys_dict_data dd2 on sp.service_category = dd2.dict_value and dd2.dict_type = 'mall_service_category'
        left join sys_dict_data dd3 on sp.provider_level = dd3.dict_value and dd3.dict_type =
        'mall_service_provider_level'
        left join sys_dict_data dd4 on sp.audit_status = dd4.dict_value and dd4.dict_type = 'business_audit_status'
        where sp.id = #{id} and sp.delete_flag = '0'
    </select>

    <sql id="selectServiceProviderVo">
        <if test="q.providerNo != null  and q.providerNo != ''">
            and sp.provider_no = #{q.providerNo}
        </if>
        <if test="q.providerName != null  and q.providerName != ''">
            and sp.provider_name like concat('%', #{q.providerName}, '%')
        </if>
        <if test="q.legalPerson != null  and q.legalPerson != ''">
            and sp.legal_person = #{q.legalPerson}
        </if>
        <if test="q.contactPerson != null  and q.contactPerson != ''">
            and sp.contact_person = #{q.contactPerson}
        </if>
        <if test="q.contactPhone != null  and q.contactPhone != ''">
            and sp.contact_phone = #{q.contactPhone}
        </if>
        <if test="q.contactEmail != null  and q.contactEmail != ''">
            and sp.contact_email = #{q.contactEmail}
        </if>
        <if test="q.businessAddress != null  and q.businessAddress != ''">
            and sp.business_address = #{q.businessAddress}
        </if>
        <if test="q.registeredAddress != null  and q.registeredAddress != ''">
            and sp.registered_address = #{q.registeredAddress}
        </if>
        <if test="q.businessScope != null  and q.businessScope != ''">
            and sp.business_scope = #{q.businessScope}
        </if>
        <if test="q.providerType != null  and q.providerType != ''">
            and sp.provider_type = #{q.providerType}
        </if>
        <if test="q.auditStatus != null  and q.auditStatus != ''">
            and sp.audit_status = #{q.auditStatus}
        </if>
        <if test="q.approvalBy != null  and q.approvalBy != ''">
            and sp.approval_by = #{q.approvalBy}
        </if>
        <if test="q.serviceArea != null  and q.serviceArea != ''">
            and sp.service_area = #{q.serviceArea}
        </if>
        <if test="q.serviceCategory != null  and q.serviceCategory != ''">
            and sp.service_category = #{q.serviceCategory}
        </if>
        <if test="q.introduction != null  and q.introduction != ''">
            and sp.introduction = #{q.introduction}
        </if>
        <if test="q.userId != null  and q.userId != ''">
            and sp.user_id = #{q.userId}
        </if>
    </sql>

    <select id="selectPage" parameterType="com.shengyu.business.domain.ServiceProvider"
            resultType="com.shengyu.business.domain.dto.ServiceProviderDto">
        select
        <include refid="Base_Column_List"/>
        , dd.dict_label as providerTypeName
        , dd2.dict_label as serviceCategoryName
        , dd3.dict_label as providerLevelName
        , dd4.dict_label as auditStatusName
        from t_mall_service_provider sp
        left join sys_dict_data dd on sp.provider_type = dd.dict_value and dd.dict_type = 'mall_service_provider_type'
        left join sys_dict_data dd2 on sp.service_category = dd2.dict_value and dd2.dict_type = 'mall_service_category'
        left join sys_dict_data dd3 on sp.provider_level = dd3.dict_value and dd3.dict_type =
        'mall_service_provider_level'
        left join sys_dict_data dd4 on sp.audit_status = dd4.dict_value and dd4.dict_type = 'business_audit_status'
        where sp.delete_flag = '0'
        <include refid="selectServiceProviderVo"/>
    </select>

    <select id="selectList" parameterType="com.shengyu.business.domain.ServiceProvider"
            resultType="com.shengyu.business.domain.dto.ServiceProviderDto">
        select
        <include refid="Base_Column_List"/>
        , dd.dict_label as providerTypeName
        , dd2.dict_label as serviceCategoryName
        , dd3.dict_label as providerLevelName
        , dd4.dict_label as auditStatusName
        from t_mall_service_provider sp
        left join sys_dict_data dd on sp.provider_type = dd.dict_value and dd.dict_type = 'mall_service_provider_type'
        left join sys_dict_data dd2 on sp.service_category = dd2.dict_value and dd2.dict_type = 'mall_service_category'
        left join sys_dict_data dd3 on sp.provider_level = dd3.dict_value and dd3.dict_type =
        'mall_service_provider_level'
        left join sys_dict_data dd4 on sp.audit_status = dd4.dict_value and dd4.dict_type = 'business_audit_status'
        where sp.delete_flag = '0'
        <include refid="selectServiceProviderVo"/>
    </select>
</mapper>