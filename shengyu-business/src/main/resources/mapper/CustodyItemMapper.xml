<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shengyu.business.mapper.BusinessCustodyItemMapper">
    <!-- 结果映射 -->
    <resultMap id="CustodySingleResult" type="com.shengyu.business.domain.CustodyItem">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="content" property="content" jdbcType="VARCHAR"/>
        <result column="unit_price" property="unitPrice" jdbcType="DECIMAL"/>
        <result column="unit" property="unit" jdbcType="VARCHAR"/>
        <result column="subsidy" property="subsidy" jdbcType="DECIMAL"/>
        <result column="discount" property="discount" jdbcType="DECIMAL"/>
        <result column="crop_type" property="cropType" jdbcType="VARCHAR"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="delete_flag" property="deleteFlag" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="DATE"/>
        <result column="update_time" property="updateTime" jdbcType="DATE"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ci.id, ci.name, ci.content, ci.unit_price, ci.unit, ci.subsidy, ci.discount,
        ci.crop_type, ci.create_by, ci.delete_flag,
        ci.create_time, ci.update_time, ci.remark
    </sql>

    <select id="selectById" parameterType="Long" resultType="com.shengyu.business.domain.dto.CustodyItemDto">
        SELECT
        <include refid="Base_Column_List"/>, dd.dict_label AS cropTypeName
        FROM t_business_custody_item ci
        LEFT JOIN sys_dict_data dd ON ci.crop_type = dd.dict_value AND dd.dict_type = 'business_crop_type'
        WHERE ci.id = #{id} AND ci.delete_flag = 0
    </select>

    <select id="selectByIdIgnoreTenant" parameterType="Long" resultType="com.shengyu.business.domain.dto.CustodyItemDto">
        SELECT
        <include refid="Base_Column_List"/>, dd.dict_label AS cropTypeName
        FROM t_business_custody_item ci
        LEFT JOIN sys_dict_data dd ON ci.crop_type = dd.dict_value AND dd.dict_type = 'business_crop_type'
        WHERE ci.id = #{id} AND ci.delete_flag = 0
    </select>

    <select id="selectList" resultType="com.shengyu.business.domain.dto.CustodyItemDto">
    SELECT
    <if test="ids !=null and ids.size > 0">
        FIELD(ci.id,
        <foreach collection="ids" item="id" separator=",">
            #{id}
        </foreach>
        ) AS `index`,
    </if>
    <include refid="Base_Column_List"/>, dd.dict_label AS cropTypeName
    FROM t_business_custody_item ci
    LEFT JOIN sys_dict_data dd ON ci.crop_type = dd.dict_value AND dd.dict_type = 'business_crop_type'
    WHERE ci.id IN
    <foreach collection="ids" item="id" separator="," open="(" close=")">
        #{id}
    </foreach>
    ORDER BY `index`
</select>

    <select id="selectListIgnoreTenant" resultType="com.shengyu.business.domain.dto.CustodyItemDto">
        SELECT
        <if test="ids !=null and ids.size > 0">
            FIELD(ci.id,
            <foreach collection="ids" item="id" separator=",">
                #{id}
            </foreach>
            ) AS `index`,
        </if>
        <include refid="Base_Column_List"/>, dd.dict_label AS cropTypeName
        FROM t_business_custody_item ci
        LEFT JOIN sys_dict_data dd ON ci.crop_type = dd.dict_value AND dd.dict_type = 'business_crop_type'
        WHERE ci.id IN
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
        ORDER BY `index`
    </select>

    <!-- MyBatis-Plus 分页查询 -->
    <select id="selectPage" resultType="com.shengyu.business.domain.dto.CustodyItemDto">
        SELECT
        <include refid="Base_Column_List"/>, dd.dict_label AS cropTypeName
        FROM t_business_custody_item ci
        LEFT JOIN sys_dict_data dd ON ci.crop_type = dd.dict_value AND dd.dict_type = 'business_crop_type'
        WHERE ci.delete_flag = 0
        <if test="q != null">
            <if test="q.name != null and q.name != ''">
                AND ci.name LIKE CONCAT(#{q.name}, '%')
            </if>
            <if test="q.cropType != null and q.cropType != ''">
                AND ci.crop_type = #{q.cropType}
            </if>
        </if>
    </select>
</mapper>