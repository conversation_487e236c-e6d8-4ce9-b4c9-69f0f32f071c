<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shengyu.business.mapper.BusinessPlaneServeMapper">
    
    <resultMap id="BaseResultMap" type="com.shengyu.business.domain.PlaneServe">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="serveNo" column="serve_no" jdbcType="VARCHAR"/>
        <result property="serveName" column="serve_name" jdbcType="VARCHAR"/>
        <result property="serveType" column="serve_type" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="TINYINT"/>
        <result property="serveBanner" column="serve_banner" jdbcType="VARCHAR"/>
        <result property="serveDescription" column="serve_description" jdbcType="VARCHAR"/>
        <result property="unitPrice" column="unit_price" jdbcType="DECIMAL"/>
        <result property="deptId" column="dept_id" jdbcType="BIGINT"/>
        <result property="deleteFlag" column="delete_flag" jdbcType="TINYINT"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ps.id,
        ps.serve_no,
        ps.serve_name,
        ps.serve_type,
        ps.status,
        ps.serve_banner,
        ps.serve_description,
        ps.unit_price,
        ps.dept_id,
        ps.delete_flag,
        ps.create_by,
        ps.create_time,
        ps.update_by,
        ps.update_time,
        ps.tenant_id,
        ps.remark
    </sql>

    <select id="selectPage" resultType="com.shengyu.business.domain.dto.PlaneServeDto">
        SELECT
            <include refid="Base_Column_List"/>,
            dd.dict_label AS serveTypeName,
            dd2.dict_label AS statusName
        FROM t_business_plane_serve ps
            LEFT JOIN sys_dict_data dd ON ps.serve_type = dd.dict_value 
                AND dd.dict_type = 'business_plane_serve_type'
            LEFT JOIN sys_dict_data dd2 ON ps.status = dd2.dict_value 
                AND dd2.dict_type = 'business_shelf_status'
        WHERE ps.delete_flag = 0
        <if test="q.serveNo != null and q.serveNo != ''">
            AND ps.serve_no LIKE CONCAT(#{q.serveNo}, '%')
        </if>
        <if test="q.serveName != null and q.serveName != ''">
            AND ps.serve_name LIKE CONCAT(#{q.serveName}, '%')
        </if>
        <if test="q.serveType != null and q.serveType != ''">
            AND ps.serve_type = #{q.serveType}
        </if>
        <if test="q.status != null">
            AND ps.status = #{q.status}
        </if>
        <if test="q.unitPrice != null and q.unitPrice != ''">
            AND ps.unit_price = #{q.unitPrice}
        </if>
        <if test="q.serveDescription != null and q.serveDescription != ''">
            AND ps.serve_description LIKE CONCAT('%', #{q.serveDescription}, '%')
        </if>
    </select>

    <select id="selectById" resultType="com.shengyu.business.domain.dto.PlaneServeDto">
        SELECT
            <include refid="Base_Column_List"/>,
            dd.dict_label AS serveTypeName,
            dd2.dict_label AS statusName
        FROM t_business_plane_serve ps
            LEFT JOIN sys_dict_data dd ON ps.serve_type = dd.dict_value 
                AND dd.dict_type = 'business_plane_serve_type'
            LEFT JOIN sys_dict_data dd2 ON ps.status = dd2.dict_value 
                AND dd2.dict_type = 'business_shelf_status'
        WHERE ps.id = #{id} 
            AND ps.delete_flag = 0
    </select>

    <select id="selectByServeType" resultType="com.shengyu.business.domain.PlaneServe">
        SELECT
            <include refid="Base_Column_List"/>
        FROM t_business_plane_serve ps
        WHERE ps.delete_flag = 0
          AND ps.status = 1
          AND ps.serve_type = #{serveType}
        LIMIT 1
    </select>

    <select id="selectByServeTypeIgnoreTenant" resultType="com.shengyu.business.domain.PlaneServe">
        SELECT
            <include refid="Base_Column_List"/>
        FROM t_business_plane_serve ps
        WHERE ps.delete_flag = 0
          AND ps.status = 1
          AND ps.serve_type = #{serveType}
        LIMIT 1
    </select>

    <select id="selectByIdIgnoreTenant" resultType="com.shengyu.business.domain.PlaneServe">
        SELECT
            <include refid="Base_Column_List"/>
        FROM t_business_plane_serve ps
        WHERE ps.id = #{id}
          AND ps.delete_flag = 0
    </select>

</mapper>