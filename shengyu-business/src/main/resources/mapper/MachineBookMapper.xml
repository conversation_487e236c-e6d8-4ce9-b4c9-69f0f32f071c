<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shengyu.business.mapper.BusinessMachineBookMapper">
    <resultMap id="BaseResultMap" type="com.shengyu.business.domain.MachineBook" autoMapping="true">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="bookNo" column="book_no" jdbcType="VARCHAR"/>
        <result property="bookUserId" column="book_user_id" jdbcType="BIGINT"/>
        <result property="serveType" column="serve_type" jdbcType="VARCHAR"/>
        <result property="serveId" column="serve_id" jdbcType="BIGINT"/>
        <result property="cropType" column="crop_type" jdbcType="VARCHAR"/>
        <result property="bookStatus" column="book_status" jdbcType="VARCHAR"/>
        <result property="contactName" column="contact_name" jdbcType="VARCHAR"/>
        <result property="phone" column="phone" jdbcType="VARCHAR"/>
        <result property="idCard" column="id_card" jdbcType="VARCHAR"/>
        <result property="plotCount" column="plot_count" jdbcType="DECIMAL"/>
        <result property="area" column="area" jdbcType="DECIMAL"/>
        <result property="addressRegion" column="address_region" jdbcType="VARCHAR"/>
        <result property="addressDetail" column="address_detail" jdbcType="VARCHAR"/>
        <result property="expirationDate" column="expiration_date" jdbcType="TIMESTAMP"/>
        <result property="deleteFlag" column="delete_flag" jdbcType="TINYINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        mb.id,
        mb.book_no,
        mb.book_user_id,
        mb.serve_type,
        mb.serve_id,
        mb.crop_type,
        mb.book_status,
        mb.contact_name,
        mb.phone,
        mb.id_card,
        mb.plot_count,
        mb.area,
        mb.dept_id,
        mb.address_detail,
        mb.expiration_date,
        mb.delete_flag,
        mb.create_time,
        mb.update_time,
        mb.tenant_id,
        mb.remark
    </sql>

    <select id="selectPage" resultType="com.shengyu.business.domain.dto.MachineBookDto">
        SELECT
        <include refid="Base_Column_List"/>,
        type_dict.dict_label AS serveTypeName,
        crop_dict.dict_label AS cropTypeName,
        status_dict.dict_label AS bookStatusName,
        dept.dept_name AS deptName
        FROM t_business_machine_book mb
        LEFT JOIN sys_dict_data type_dict
        ON mb.serve_type = type_dict.dict_value
        AND type_dict.dict_type = 'business_machine_serve_type'
        LEFT JOIN sys_dict_data crop_dict
        ON mb.crop_type = crop_dict.dict_value
        AND crop_dict.dict_type = 'business_crop_type'
        LEFT JOIN sys_dict_data status_dict
        ON mb.book_status = status_dict.dict_value
        AND status_dict.dict_type = 'business_book_status'
        LEFT JOIN sys_dept dept
        ON dept.dept_id = mb.dept_id
        WHERE mb.delete_flag = 0
        <if test="q.bookNo != null and q.bookNo != ''">
            AND mb.book_no = #{q.bookNo}
        </if>
        <if test="q.serveType != null and q.serveType != ''">
            AND mb.serve_type = #{q.serveType}
        </if>
        <if test="q.cropType != null and q.cropType != ''">
            AND mb.crop_type = #{q.cropType}
        </if>
        <if test="q.bookStatus != null and q.bookStatus != ''">
            AND mb.book_status = #{q.bookStatus}
        </if>
        <if test="q.contactName != null and q.contactName != ''">
            AND mb.contact_name LIKE CONCAT(#{q.contactName}, '%')
        </if>
        <if test="q.phone != null and q.phone != ''">
            AND mb.phone LIKE CONCAT(#{q.phone}, '%')
        </if>
        <if test="q.idCard != null and q.idCard != ''">
            AND mb.id_card LIKE CONCAT(#{q.idCard}, '%')
        </if>
        <if test="q.addressRegion != null and q.addressRegion != ''">
            AND mb.address_region = #{q.addressRegion}
        </if>
        <if test="q.addressDetail != null and q.addressDetail != ''">
            AND mb.address_detail LIKE CONCAT('%', #{q.addressDetail}, '%')
        </if>
        <if test="q.expirationDate != null">
            AND mb.expiration_date = #{q.expirationDate}
        </if>
        <if test="q.bookUserId != null">
            AND mb.book_user_id = #{q.bookUserId}
        </if>
        <if test="q.plotCount != null">
            AND mb.plot_count = #{q.plotCount}
        </if>
        <if test="q.area != null">
            AND mb.area = #{q.area}
        </if>
    </select>

    <select id="selectPageIgnoreTenant" resultType="com.shengyu.business.domain.dto.MachineBookDto">
        SELECT
        <include refid="Base_Column_List"/>,
        type_dict.dict_label AS serveTypeName,
        crop_dict.dict_label AS cropTypeName,
        status_dict.dict_label AS bookStatusName,
        dept.dept_name AS deptName
        FROM t_business_machine_book mb
        LEFT JOIN sys_dict_data type_dict
        ON mb.serve_type = type_dict.dict_value
        AND type_dict.dict_type = 'business_machine_serve_type'
        LEFT JOIN sys_dict_data crop_dict
        ON mb.crop_type = crop_dict.dict_value
        AND crop_dict.dict_type = 'business_crop_type'
        LEFT JOIN sys_dict_data status_dict
        ON mb.book_status = status_dict.dict_value
        AND status_dict.dict_type = 'business_book_status'
        LEFT JOIN sys_dept dept
        ON dept.dept_id = mb.dept_id
        WHERE mb.delete_flag = 0
        <if test="q.bookNo != null and q.bookNo != ''">
            AND mb.book_no = #{q.bookNo}
        </if>
        <if test="q.serveType != null and q.serveType != ''">
            AND mb.serve_type = #{q.serveType}
        </if>
        <if test="q.cropType != null and q.cropType != ''">
            AND mb.crop_type = #{q.cropType}
        </if>
        <if test="q.bookStatus != null and q.bookStatus != ''">
            AND mb.book_status = #{q.bookStatus}
        </if>
        <if test="q.contactName != null and q.contactName != ''">
            AND mb.contact_name LIKE CONCAT(#{q.contactName}, '%')
        </if>
        <if test="q.phone != null and q.phone != ''">
            AND mb.phone LIKE CONCAT(#{q.phone}, '%')
        </if>
        <if test="q.idCard != null and q.idCard != ''">
            AND mb.id_card LIKE CONCAT(#{q.idCard}, '%')
        </if>
        <if test="q.addressRegion != null and q.addressRegion != ''">
            AND mb.address_region = #{q.addressRegion}
        </if>
        <if test="q.addressDetail != null and q.addressDetail != ''">
            AND mb.address_detail LIKE CONCAT('%', #{q.addressDetail}, '%')
        </if>
        <if test="q.expirationDate != null">
            AND mb.expiration_date = #{q.expirationDate}
        </if>
        <if test="q.bookUserId != null">
            AND mb.book_user_id = #{q.bookUserId}
        </if>
        <if test="q.plotCount != null">
            AND mb.plot_count = #{q.plotCount}
        </if>
        <if test="q.area != null">
            AND mb.area = #{q.area}
        </if>
    </select>

    <select id="selectById" resultType="com.shengyu.business.domain.dto.MachineBookDto">
        SELECT
        <include refid="Base_Column_List"/>,
        type_dict.dict_label AS serveTypeName,
        crop_dict.dict_label AS cropTypeName,
        status_dict.dict_label AS bookStatusName,
        sa.mer_name AS addressRegionName
        FROM t_business_machine_book mb
        LEFT JOIN sys_dict_data type_dict
        ON mb.serve_type = type_dict.dict_value
        AND type_dict.dict_type = 'business_machine_serve_type'
        LEFT JOIN sys_dict_data crop_dict
        ON mb.crop_type = crop_dict.dict_value
        AND crop_dict.dict_type = 'business_crop_type'
        LEFT JOIN sys_dict_data status_dict
        ON mb.book_status = status_dict.dict_value
        AND status_dict.dict_type = 'business_book_status'
        LEFT JOIN sys_area sa
        ON sa.code = mb.address_region
        WHERE mb.id = #{id}
    </select>

    <select id="selectByIdIgnoreTenant" resultType="com.shengyu.business.domain.MachineBook">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_business_machine_book mb
        WHERE mb.id = #{id}
    </select>

    <!-- 忽略租户的更新（根据ID） -->
    <update id="updateByIdIgnoreTenant">
        UPDATE t_business_machine_book
        <set>
            <if test="et.serveType != null">serve_type = #{et.serveType},</if>
            <if test="et.serveId != null">serve_id = #{et.serveId},</if>
            <if test="et.cropType != null">crop_type = #{et.cropType},</if>
            <if test="et.bookStatus != null">book_status = #{et.bookStatus},</if>
            <if test="et.contactName != null">contact_name = #{et.contactName},</if>
            <if test="et.phone != null">phone = #{et.phone},</if>
            <if test="et.idCard != null">id_card = #{et.idCard},</if>
            <if test="et.plotCount != null">plot_count = #{et.plotCount},</if>
            <if test="et.area != null">area = #{et.area},</if>
            <if test="et.addressRegion != null">address_region = #{et.addressRegion},</if>
            <if test="et.addressDetail != null">address_detail = #{et.addressDetail},</if>
            <if test="et.expirationDate != null">expiration_date = #{et.expirationDate},</if>
            <if test="et.remark != null">remark = #{et.remark},</if>
        </set>
        WHERE id = #{et.id}
    </update>

    <!-- 忽略租户的删除（根据ID） -->
    <delete id="deleteByIdIgnoreTenant">
        DELETE FROM t_business_machine_book WHERE id = #{id}
    </delete>

    <!-- 忽略租户的新增（插入） -->
    <insert id="insertIgnoreTenant" useGeneratedKeys="true" keyProperty="et.id">
        INSERT INTO t_business_machine_book
        <trim prefix="(" suffix=")" suffixOverrides=",">
            book_no,
            book_user_id,
            serve_type,
            serve_id,
            tenant_id,
            <if test="et.cropType != null and et.cropType != ''">crop_type,</if>
            <if test="et.bookStatus != null and et.bookStatus != ''">book_status,</if>
            <if test="et.contactName != null and et.contactName != ''">contact_name,</if>
            <if test="et.phone != null and et.phone != ''">phone,</if>
            <if test="et.idCard != null and et.idCard != ''">id_card,</if>
            <if test="et.plotCount != null">plot_count,</if>
            <if test="et.area != null">area,</if>
            <if test="et.addressRegion != null and et.addressRegion != ''">address_region,</if>
            <if test="et.addressDetail != null and et.addressDetail != ''">address_detail,</if>
            <if test="et.expirationDate != null">expiration_date,</if>
            <if test="et.remark != null and et.remark != ''">remark,</if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            #{et.bookNo},
            #{et.bookUserId},
            #{et.serveType},
            #{et.serveId},
            #{et.tenantId},
            <if test="et.cropType != null and et.cropType != ''">#{et.cropType},</if>
            <if test="et.bookStatus != null and et.bookStatus != ''">#{et.bookStatus},</if>
            <if test="et.contactName != null and et.contactName != ''">#{et.contactName},</if>
            <if test="et.phone != null and et.phone != ''">#{et.phone},</if>
            <if test="et.idCard != null and et.idCard != ''">#{et.idCard},</if>
            <if test="et.plotCount != null">#{et.plotCount},</if>
            <if test="et.area != null">#{et.area},</if>
            <if test="et.addressRegion != null and et.addressRegion != ''">#{et.addressRegion},</if>
            <if test="et.addressDetail != null and et.addressDetail != ''">#{et.addressDetail},</if>
            <if test="et.expirationDate != null">#{et.expirationDate},</if>
            <if test="et.remark != null and et.remark != ''">#{et.remark},</if>
        </trim>
    </insert>
</mapper>
