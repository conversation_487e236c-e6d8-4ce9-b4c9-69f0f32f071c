<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shengyu.business.mapper.BusinessSupplyDemandMapper">
    <resultMap id="BaseResultMap" type="com.shengyu.business.domain.SupplyDemand">
        <id property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="infoTitle" column="info_title"/>
        <result property="infoType" column="info_type"/>
        <result property="serveType" column="serve_type"/>
        <result property="cropType" column="crop_type"/>
        <result property="contactName" column="contact_name"/>
        <result property="phoneNumber" column="phone_number"/>
        <result property="idCard" column="id_card"/>
        <result property="imageUrl" column="image_url"/>
        <result property="province" column="province"/>
        <result property="city" column="city"/>
        <result property="district" column="district"/>
        <result property="detailedAddress" column="address_detail"/>
        <result property="description" column="description"/>
        <result property="publishTime" column="publish_time"/>
        <result property="auditStatus" column="audit_status"/>
        <result property="expirationDate" column="expiration_date" jdbcType="TIMESTAMP"/>
        <result property="reviewerId" column="reviewer_id"/>
        <result property="reviewTime" column="review_time"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="Base_Column_List">
        sd.id,
        sd.user_id,
        sd.info_title,
        sd.info_type,
        sd.serve_type,
        sd.crop_type,
        sd.contact_name,
        sd.phone_number,
        sd.id_card,
        sd.image_url,
        sd.dept_id,
        sd.address_detail,
        sd.description,
        sd.publish_time,
        sd.audit_status,
        sd.expiration_date,
        sd.reviewer_id,
        sd.review_time,
        sd.tenant_id,
        sd.create_time,
        sd.update_time
    </sql>

    <!-- 公共查询列，包含关联字典和地区信息 -->
    <sql id="Query_Column_List">
        <include refid="Base_Column_List"/>,
        dd.dict_label AS infoTypeName,
        dd2.dict_label AS cropTypeName,
        dd3.dict_label AS auditStatusName,
        dd4.dict_label AS serveTypeName,
        dept.dept_name AS deptName
    </sql>

    <!-- 公共表连接 -->
    <sql id="Common_Joins">
        FROM t_business_supply_demand sd
        LEFT JOIN sys_dict_data dd
        ON sd.info_type = dd.dict_value
        AND dd.dict_type = 'business_supply_demand_type'
        LEFT JOIN sys_dict_data dd2
        ON sd.crop_type = dd2.dict_value
        AND dd2.dict_type = 'business_crop_type'
        LEFT JOIN sys_dict_data dd3
        ON sd.audit_status = dd3.dict_value
        AND dd3.dict_type = 'business_audit_status'
        LEFT JOIN sys_dict_data dd4
        ON sd.serve_type = dd4.dict_value
        AND dd4.dict_type = 'business_supply_demand_serve_type'
        LEFT JOIN sys_dept dept ON sd.dept_id = dept.dept_id
    </sql>

    <!-- 公共查询条件 -->
    <sql id="Common_Where_Conditions">
        <if test="sd.auditStatus != null">
            AND audit_status = #{sd.auditStatus.name}
        </if>
        <if test="sd.infoType != null and sd.infoType != ''">
            AND info_type = #{sd.infoType}
        </if>
        <if test="sd.serveType != null and sd.serveType != ''">
            AND serve_type = #{sd.serveType}
        </if>
        <if test="sd.cropType != null and sd.cropType != ''">
            AND crop_type = #{sd.cropType}
        </if>
        <if test="sd.expirationDate != null">
            AND expiration_date = #{sd.expirationDate}
        </if>
        <if test="sd.contactName != null and sd.contactName != ''">
            AND contact_name = #{sd.contactName}
        </if>
        <if test="sd.phoneNumber != null and sd.phoneNumber != ''">
            AND phone_number = #{sd.phoneNumber}
        </if>
        <if test="sd.userId != null and sd.userId != ''">
            AND user_id = #{sd.userId}
        </if>
        <if test="sd.deptId != null">
            AND dept_id = #{sd.deptId}
        </if>
        <if test="sd.infoTitle != null and sd.infoTitle != ''">
            AND info_title LIKE CONCAT('%', #{sd.infoTitle}, '%')
        </if>
    </sql>

    <!-- MyBatis-Plus 分页查询，参数采用 @Param("sd") 别名 -->
    <select id="getPage" resultType="com.shengyu.business.domain.dto.SupplyDemandDto">
        SELECT
        SUBSTRING_INDEX(sd.image_url, ',', 1) AS image_url,
        <include refid="Query_Column_List"/>
        <include refid="Common_Joins"/>
        WHERE 1 = 1
        <include refid="Common_Where_Conditions"/>
    </select>

    <!-- 查询供需信息列表，参数采用 @Param("sd") 别名 -->
    <select id="getList" resultType="com.shengyu.business.domain.dto.SupplyDemandDto">
        SELECT
        SUBSTRING_INDEX(sd.image_url, ',', 1) AS image_url,
        <include refid="Query_Column_List"/>
        <include refid="Common_Joins"/>
        WHERE 1 = 1
        <include refid="Common_Where_Conditions"/>
    </select>

    <!-- 忽略租户的分页查询，与 getPage 逻辑一致，参数采用 @Param("sd") 别名 -->
    <select id="getPageIgnoreTenant" resultType="com.shengyu.business.domain.dto.SupplyDemandDto">
        SELECT
        SUBSTRING_INDEX(sd.image_url, ',', 1) AS image_url,
        <include refid="Query_Column_List"/>
        <include refid="Common_Joins"/>
        WHERE 1 = 1
        <include refid="Common_Where_Conditions"/>
    </select>



    <insert id="insert" parameterType="com.shengyu.business.domain.SupplyDemand">
        INSERT INTO t_business_supply_demand
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="infoTitle != null">info_title,</if>
            <if test="infoType != null">info_type,</if>
            <if test="serveType != null">serve_type,</if>
            <if test="cropType != null">crop_type,</if>
            <if test="contactName != null">contact_name,</if>
            <if test="phoneNumber != null">phone_number,</if>
            <if test="idCard != null">id_card,</if>
            <if test="imageUrl != null">image_url,</if>
            <if test="province != null">province,</if>
            <if test="city != null">city,</if>
            <if test="district != null">district,</if>
            <if test="detailedAddress != null">address_detail,</if>
            <if test="description != null">description,</if>
            <if test="publishTime != null">publish_time,</if>
            <if test="auditStatus != null">audit_status,</if>
            <if test="expirationDate != null">expiration_date,</if>
            <if test="reviewerId != null">reviewer_id,</if>
            <if test="reviewTime != null">review_time,</if>
            <if test="tenantId != null">tenant_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="infoTitle != null">#{infoTitle},</if>
            <if test="infoType != null">#{infoType},</if>
            <if test="serveType != null">#{serveType},</if>
            <if test="cropType != null">#{cropType},</if>
            <if test="contactName != null">#{contactName},</if>
            <if test="phoneNumber != null">#{phoneNumber},</if>
            <if test="idCard != null">#{idCard},</if>
            <if test="imageUrl != null">#{imageUrl},</if>
            <if test="province != null">#{province},</if>
            <if test="city != null">#{city},</if>
            <if test="district != null">#{district},</if>
            <if test="detailedAddress != null">#{detailedAddress},</if>
            <if test="description != null">#{description},</if>
            <if test="publishTime != null">#{publishTime},</if>
            <if test="auditStatus != null">#{auditStatus},</if>
            <if test="expirationDate != null">#{expirationDate},</if>
            <if test="reviewerId != null">#{reviewerId},</if>
            <if test="reviewTime != null">#{reviewTime},</if>
            <if test="tenantId != null">#{tenantId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.shengyu.business.domain.SupplyDemand">
        UPDATE t_business_supply_demand
        <set>
            <if test="infoTitle != null">info_title = #{infoTitle},</if>
            <if test="infoType != null">info_type = #{infoType},</if>
            <if test="serveType != null">serve_type = #{serveType},</if>
            <if test="cropType != null">crop_type = #{cropType},</if>
            <if test="contactName != null">contact_name = #{contactName},</if>
            <if test="phoneNumber != null">phone_number = #{phoneNumber},</if>
            <if test="idCard != null">id_card = #{idCard},</if>
            <if test="imageUrl != null">image_url = #{imageUrl},</if>
            <if test="province != null">province = #{province},</if>
            <if test="city != null">city = #{city},</if>
            <if test="district != null">district = #{district},</if>
            <if test="detailedAddress != null">address_detail = #{detailedAddress},</if>
            <if test="description != null">description = #{description},</if>
            <if test="publishTime != null">publish_time = #{publishTime},</if>
            <if test="auditStatus != null">audit_status = #{auditStatus},</if>
            <if test="expirationDate != null">expiration_date = #{expirationDate},</if>
            <if test="reviewerId != null">reviewer_id = #{reviewerId},</if>
            <if test="reviewTime != null">review_time = #{reviewTime},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </set>
        WHERE id = #{id}
    </update>

    <sql id="selectCommon">
        SELECT
        <include refid="Query_Column_List"/>
        <include refid="Common_Joins"/>
        WHERE sd.id = #{id}
    </sql>

    <select id="selectById" resultType="com.shengyu.business.domain.dto.SupplyDemandDto">
        <include refid="selectCommon"/>
    </select>

    <select id="selectValidById" resultType="com.shengyu.business.domain.dto.SupplyDemandDto">
        <include refid="selectCommon"/>
        AND sd.delete_flag = 0 AND sd.audit_status = 'PASS'
    </select>

    <!-- 忽略租户的单条查询 -->
    <sql id="selectCommonIgnoreTenant">
        SELECT
        <include refid="Query_Column_List"/>
        <include refid="Common_Joins"/>
        WHERE sd.id = #{id}
    </sql>

    <select id="selectByIdIgnoreTenant" resultType="com.shengyu.business.domain.dto.SupplyDemandDto">
        <include refid="selectCommonIgnoreTenant"/>
    </select>

    <select id="selectValidByIdIgnoreTenant" resultType="com.shengyu.business.domain.dto.SupplyDemandDto">
        <include refid="selectCommonIgnoreTenant"/>
        AND sd.delete_flag = 0 AND sd.audit_status = 'PASS'
    </select>

    <!-- 忽略租户的保存操作 -->
    <insert id="saveIgnoreTenant" parameterType="com.shengyu.business.domain.SupplyDemand">
        INSERT INTO t_business_supply_demand
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="entity.userId != null">user_id,</if>
            <if test="entity.infoTitle != null">info_title,</if>
            <if test="entity.infoType != null">info_type,</if>
            <if test="entity.serveType != null">serve_type,</if>
            <if test="entity.cropType != null">crop_type,</if>
            <if test="entity.contactName != null">contact_name,</if>
            <if test="entity.phoneNumber != null">phone_number,</if>
            <if test="entity.idCard != null">id_card,</if>
            <if test="entity.imageUrl != null">image_url,</if>
            <if test="entity.province != null">province,</if>
            <if test="entity.city != null">city,</if>
            <if test="entity.district != null">district,</if>
            <if test="entity.detailedAddress != null">address_detail,</if>
            <if test="entity.description != null">description,</if>
            <if test="entity.publishTime != null">publish_time,</if>
            <if test="entity.auditStatus != null">audit_status,</if>
            <if test="entity.expirationDate != null">expiration_date,</if>
            <if test="entity.reviewerId != null">reviewer_id,</if>
            <if test="entity.reviewTime != null">review_time,</if>
            <if test="entity.tenantId != null">tenant_id,</if>
            <if test="entity.createTime != null">create_time,</if>
            <if test="entity.updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="entity.userId != null">#{entity.userId},</if>
            <if test="entity.infoTitle != null">#{entity.infoTitle},</if>
            <if test="entity.infoType != null">#{entity.infoType},</if>
            <if test="entity.serveType != null">#{entity.serveType},</if>
            <if test="entity.cropType != null">#{entity.cropType},</if>
            <if test="entity.contactName != null">#{entity.contactName},</if>
            <if test="entity.phoneNumber != null">#{entity.phoneNumber},</if>
            <if test="entity.idCard != null">#{entity.idCard},</if>
            <if test="entity.imageUrl != null">#{entity.imageUrl},</if>
            <if test="entity.province != null">#{entity.province},</if>
            <if test="entity.city != null">#{entity.city},</if>
            <if test="entity.district != null">#{entity.district},</if>
            <if test="entity.detailedAddress != null">#{entity.detailedAddress},</if>
            <if test="entity.description != null">#{entity.description},</if>
            <if test="entity.publishTime != null">#{entity.publishTime},</if>
            <if test="entity.auditStatus != null">#{entity.auditStatus.name},</if>
            <if test="entity.expirationDate != null">#{entity.expirationDate},</if>
            <if test="entity.reviewerId != null">#{entity.reviewerId},</if>
            <if test="entity.reviewTime != null">#{entity.reviewTime},</if>
            <if test="entity.tenantId != null">#{entity.tenantId},</if>
            <if test="entity.createTime != null">#{entity.createTime},</if>
            <if test="entity.updateTime != null">#{entity.updateTime},</if>
        </trim>
    </insert>

    <!-- 忽略租户的更新操作 -->
    <update id="updateByIdIgnoreTenant" parameterType="com.shengyu.business.domain.SupplyDemand">
        UPDATE t_business_supply_demand
        <set>
            <if test="entity.userId != null">user_id = #{entity.userId},</if>
            <if test="entity.infoTitle != null">info_title = #{entity.infoTitle},</if>
            <if test="entity.infoType != null">info_type = #{entity.infoType},</if>
            <if test="entity.serveType != null">serve_type = #{entity.serveType},</if>
            <if test="entity.cropType != null">crop_type = #{entity.cropType},</if>
            <if test="entity.contactName != null">contact_name = #{entity.contactName},</if>
            <if test="entity.phoneNumber != null">phone_number = #{entity.phoneNumber},</if>
            <if test="entity.idCard != null">id_card = #{entity.idCard},</if>
            <if test="entity.imageUrl != null">image_url = #{entity.imageUrl},</if>
            <if test="entity.province != null">province = #{entity.province},</if>
            <if test="entity.city != null">city = #{entity.city},</if>
            <if test="entity.district != null">district = #{entity.district},</if>
            <if test="entity.detailedAddress != null">address_detail = #{entity.detailedAddress},</if>
            <if test="entity.description != null">description = #{entity.description},</if>
            <if test="entity.publishTime != null">publish_time = #{entity.publishTime},</if>
            <if test="entity.auditStatus != null">audit_status = #{entity.auditStatus.name},</if>
            <if test="entity.expirationDate != null">expiration_date = #{entity.expirationDate},</if>
            <if test="entity.reviewerId != null">reviewer_id = #{entity.reviewerId},</if>
            <if test="entity.reviewTime != null">review_time = #{entity.reviewTime},</if>
            <if test="entity.tenantId != null">tenant_id = #{entity.tenantId},</if>
            <if test="entity.updateTime != null">update_time = #{entity.updateTime},</if>
        </set>
        WHERE id = #{entity.id}
    </update>

    <!-- 忽略租户的删除操作 -->
    <delete id="removeByIdIgnoreTenant">
        DELETE FROM t_business_supply_demand WHERE id = #{id}
    </delete>
</mapper>
