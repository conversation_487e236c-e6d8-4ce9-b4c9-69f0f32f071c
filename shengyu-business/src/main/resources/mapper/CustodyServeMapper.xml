<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shengyu.business.mapper.BusinessCustodyServeMapper">
    <resultMap id="BaseResultMap" type="com.shengyu.business.domain.CustodyServe">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="custodyCategory" column="custody_category" jdbcType="VARCHAR"/>
        <result property="serviceItems" column="service_items" jdbcType="VARCHAR"/>
        <result property="unitPrice" column="unit_price" jdbcType="DECIMAL"/>
        <result property="unit" column="unit" jdbcType="VARCHAR"/>
        <result property="subsidy" column="subsidy" jdbcType="DECIMAL"/>
        <result property="discount" column="discount" jdbcType="DECIMAL"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="tags" column="tags" jdbcType="VARCHAR"/>
        <result property="cropType" column="crop_type" jdbcType="VARCHAR"/>
        <result property="imageUrl" column="image_url" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="TINYINT"/>
        <result property="serviceProvider" column="service_provider" jdbcType="VARCHAR"/>
        <result property="serviceProviderId" column="service_provider_id" jdbcType="BIGINT"/>
        <result property="deptId" column="dept_id" jdbcType="BIGINT"/>
        <result property="deleteFlag" column="delete_flag" jdbcType="TINYINT"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        cs.id, cs.name, cs.custody_category, cs.service_items,
        cs.unit_price, cs.unit, cs.subsidy,
        cs.discount, cs.description, cs.tags,
        cs.crop_type, cs.image_url, cs.status,
        cs.service_provider, cs.service_provider_id, cs.dept_id, cs.delete_flag, cs.create_by,
        cs.create_time, cs.update_by, cs.update_time,
        cs.tenant_id, cs.remark
    </sql>

    <select id="selectPage" resultType="com.shengyu.business.domain.dto.CustodyServeDto">
        SELECT
        <include refid="Base_Column_List"/>,
        dd.dict_label AS cropTypeName,
        dd2.dict_label AS statusName,
        dd3.dict_label AS custodyCategoryName,
        dept.dept_name AS deptName
        FROM t_business_custody_serve cs
        LEFT JOIN sys_dict_data dd ON cs.crop_type = dd.dict_value AND dd.dict_type = 'business_crop_type'
        LEFT JOIN sys_dict_data dd2 ON cs.status = dd2.dict_value AND dd2.dict_type = 'business_shelf_status'
        LEFT JOIN sys_dict_data dd3 ON cs.custody_category = dd3.dict_value AND dd3.dict_type = 'business_custody_type'
        LEFT JOIN sys_dept dept ON dept.dept_id = cs.dept_id
        WHERE cs.delete_flag = 0
        <if test="q.name != null and q.name != ''">
            AND cs.name LIKE CONCAT(#{q.name}, '%')
        </if>
        <if test="q.status != null">
            AND cs.status = #{q.status}
        </if>
        <if test="q.custodyCategory != null">
            AND cs.custody_category = #{q.custodyCategory.name}
        </if>
        <if test="q.cropType != null and q.cropType != ''">
            AND cs.crop_type = #{q.cropType}
        </if>
        <if test="q.deptId != null">
            AND cs.dept_id = #{q.deptId}
        </if>
        <if test="q.unitPrice != null">
            AND cs.unit_price = #{q.unitPrice}
        </if>
        <if test="q.serviceProvider != null and q.serviceProvider != ''">
            AND cs.service_provider LIKE CONCAT(#{q.serviceProvider}, '%')
        </if>
        <if test="q.serviceProviderId != null">
            AND cs.service_provider_id = #{q.serviceProviderId}
        </if>
        <if test="q.description != null and q.description != ''">
            AND cs.description LIKE CONCAT('%', #{q.description}, '%')
        </if>
        <if test="q.tags != null and q.tags != ''">
            AND cs.tags LIKE CONCAT('%', #{q.tags}, '%')
        </if>
        <if test="q.serviceItems != null and q.serviceItems != ''">
            AND cs.service_items LIKE CONCAT('%', #{q.serviceItems}, '%')
        </if>
        ORDER BY cs.create_time DESC
    </select>

    <select id="selectById" resultType="com.shengyu.business.domain.dto.CustodyServeDto">
        SELECT
        <include refid="Base_Column_List"/>,
        dd.dict_label AS cropTypeName,
        dd2.dict_label AS statusName,
        dd3.dict_label AS custodyCategoryName,
        dept.dept_name AS deptName
        FROM t_business_custody_serve cs
        LEFT JOIN sys_dict_data dd ON cs.crop_type = dd.dict_value AND dd.dict_type = 'business_crop_type'
        LEFT JOIN sys_dict_data dd2 ON cs.status = dd2.dict_value AND dd2.dict_type = 'business_shelf_status'
        LEFT JOIN sys_dict_data dd3 ON cs.custody_category = dd3.dict_value AND dd3.dict_type = 'business_custody_type'
        LEFT JOIN sys_dept dept ON dept.dept_id = cs.dept_id
        WHERE cs.id = #{id}
        AND cs.delete_flag = 0
    </select>

    <select id="itemIdIsInServe" resultType="Integer">
        SELECT COUNT(1)
        FROM t_business_custody_serve
        WHERE delete_flag = 0
          AND FIND_IN_SET(#{singleId}, service_items) > 0
    </select>
    
    <select id="selectPageIgnoreTenant" resultType="com.shengyu.business.domain.dto.CustodyServeDto">
        SELECT
        <include refid="Base_Column_List"/>,
        dd.dict_label AS cropTypeName,
        dd2.dict_label AS statusName,
        dd3.dict_label AS custodyCategoryName,
        dept.dept_name AS deptName
        FROM t_business_custody_serve cs
        LEFT JOIN sys_dict_data dd ON cs.crop_type = dd.dict_value AND dd.dict_type = 'business_crop_type'
        LEFT JOIN sys_dict_data dd2 ON cs.status = dd2.dict_value AND dd2.dict_type = 'business_shelf_status'
        LEFT JOIN sys_dict_data dd3 ON cs.custody_category = dd3.dict_value AND dd3.dict_type = 'business_custody_type'
        LEFT JOIN sys_dept dept ON dept.dept_id = cs.dept_id
        WHERE cs.delete_flag = 0
        <if test="q.name != null and q.name != ''">
            AND cs.name LIKE CONCAT(#{q.name}, '%')
        </if>
        <if test="q.status != null">
            AND cs.status = #{q.status}
        </if>
        <if test="q.custodyCategory != null">
            AND cs.custody_category = #{q.custodyCategory.name}
        </if>
        <if test="q.cropType != null and q.cropType != ''">
            AND cs.crop_type = #{q.cropType}
        </if>
        <if test="q.deptId != null">
            AND cs.dept_id = #{q.deptId}
        </if>
        <if test="q.unitPrice != null">
            AND cs.unit_price = #{q.unitPrice}
        </if>
        <if test="q.serviceProvider != null and q.serviceProvider != ''">
            AND cs.service_provider LIKE CONCAT(#{q.serviceProvider}, '%')
        </if>
        <if test="q.serviceProviderId != null">
            AND cs.service_provider_id = #{q.serviceProviderId}
        </if>
        <if test="q.description != null and q.description != ''">
            AND cs.description LIKE CONCAT('%', #{q.description}, '%')
        </if>
        <if test="q.tags != null and q.tags != ''">
            AND cs.tags LIKE CONCAT('%', #{q.tags}, '%')
        </if>
        <if test="q.serviceItems != null and q.serviceItems != ''">
            AND cs.service_items LIKE CONCAT('%', #{q.serviceItems}, '%')
        </if>
        ORDER BY cs.create_time DESC
    </select>
    
    <select id="selectByIdIgnoreTenant" resultType="com.shengyu.business.domain.dto.CustodyServeDto">
        SELECT
        <include refid="Base_Column_List"/>,
        dd.dict_label AS cropTypeName,
        dd2.dict_label AS statusName,
        dd3.dict_label AS custodyCategoryName,
        dept.dept_name AS deptName
        FROM t_business_custody_serve cs
        LEFT JOIN sys_dict_data dd ON cs.crop_type = dd.dict_value AND dd.dict_type = 'business_crop_type'
        LEFT JOIN sys_dict_data dd2 ON cs.status = dd2.dict_value AND dd2.dict_type = 'business_shelf_status'
        LEFT JOIN sys_dict_data dd3 ON cs.custody_category = dd3.dict_value AND dd3.dict_type = 'business_custody_type'
        LEFT JOIN sys_dept dept ON dept.dept_id = cs.dept_id
        WHERE cs.id = #{id}
        AND cs.delete_flag = 0
    </select>
</mapper>
