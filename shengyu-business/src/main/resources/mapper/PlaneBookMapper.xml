<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shengyu.business.mapper.BusinessPlaneBookMapper">

    <resultMap id="BaseResultMap" type="com.shengyu.business.domain.PlaneBook">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="bookNo" column="book_no" jdbcType="VARCHAR"/>
        <result property="bookUserId" column="book_user_id" jdbcType="BIGINT"/>
        <result property="serveType" column="serve_type" jdbcType="VARCHAR"/>
        <result property="serveId" column="serve_id" jdbcType="BIGINT"/>
        <result property="bookStatus" column="book_status" jdbcType="VARCHAR"/>
        <result property="contactName" column="contact_name" jdbcType="VARCHAR"/>
        <result property="phone" column="phone" jdbcType="VARCHAR"/>
        <result property="idCard" column="id_card" jdbcType="VARCHAR"/>
        <result property="plotCount" column="plot_count" jdbcType="DECIMAL"/>
        <result property="area" column="area" jdbcType="DECIMAL"/>
        <result property="deptId" column="dept_id" jdbcType="BIGINT"/>
        <result property="addressDetail" column="address_detail" jdbcType="VARCHAR"/>
        <result property="unitPrice" column="unit_price" jdbcType="DECIMAL"/>
        <result property="expirationDate" column="expiration_date" jdbcType="TIMESTAMP"/>
        <result property="totalPrice" column="total_price" jdbcType="DECIMAL"/>
        <result property="deleteFlag" column="delete_flag" jdbcType="TINYINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        pb.id,
        pb.book_no,
        pb.book_user_id,
        pb.serve_type,
        pb.serve_id,
        pb.book_status,
        pb.contact_name,
        pb.phone,
        pb.id_card,
        pb.plot_count,
        pb.area,
        pb.dept_id,
        pb.address_detail,
        pb.unit_price,
        pb.expiration_date,
        pb.total_price,
        pb.delete_flag,
        pb.create_time,
        pb.update_time,
        pb.tenant_id,
        pb.remark
    </sql>

    <select id="selectPage" resultType="com.shengyu.business.domain.dto.PlaneBookDto">
        SELECT
            <include refid="Base_Column_List"/>,
            dd1.dict_label AS serveTypeName,
            dd2.dict_label AS bookStatusName,
            dept.dept_name AS deptName
        FROM t_business_plane_book pb
            LEFT JOIN sys_dict_data dd1
                ON pb.serve_type = dd1.dict_value
                AND dd1.dict_type = 'business_plane_serve_type'
            LEFT JOIN sys_dict_data dd2
                ON pb.book_status = dd2.dict_value
                AND dd2.dict_type = 'business_book_status'
            LEFT JOIN sys_dept dept
                ON dept.dept_id = pb.dept_id
        WHERE 1 = 1
            <if test="q.serveType != null and q.serveType != ''">
                AND pb.serve_type = #{q.serveType}
            </if>
            <if test="q.bookStatus != null and q.bookStatus != ''">
                AND pb.book_status = #{q.bookStatus}
            </if>
            <if test="q.bookUserId != null and q.bookUserId != ''">
                AND pb.book_user_id = #{q.bookUserId}
            </if>
            <if test="q.serveId != null and q.serveId != ''">
                AND pb.serve_id = #{q.serveId}
            </if>
            <if test="q.bookNo != null and q.bookNo != ''">
                AND pb.book_no = #{q.bookNo}
            </if>
            <if test="q.expirationDate != null and q.expirationDate != ''">
                AND pb.expiration_date = #{q.expirationDate}
            </if>
            <if test="q.totalPrice != null">
                AND pb.total_price = #{q.totalPrice}
            </if>
            <if test="q.contactName != null and q.contactName != ''">
                AND pb.contact_name LIKE CONCAT(#{q.contactName}, '%')
            </if>
            <if test="q.phone != null and q.phone != ''">
                AND pb.phone LIKE CONCAT(#{q.phone}, '%')
            </if>
            <if test="q.idCard != null and q.idCard != ''">
                AND pb.id_card LIKE CONCAT(#{q.idCard}, '%')
            </if>
            <if test="q.addressDetail != null and q.addressDetail != ''">
                AND pb.address_detail LIKE CONCAT('%', #{q.addressDetail}, '%')
            </if>
            <if test="q.plotCount != null">
                AND pb.plot_count = #{q.plotCount}
            </if>
            <if test="q.area != null">
                AND pb.area = #{q.area}
            </if>
    </select>

    <select id="selectPageIgnoreTenant" resultType="com.shengyu.business.domain.dto.PlaneBookDto">
        SELECT
            <include refid="Base_Column_List"/>,
            dd1.dict_label AS serveTypeName,
            dd2.dict_label AS bookStatusName,
            dept.dept_name AS deptName
        FROM t_business_plane_book pb
            LEFT JOIN sys_dict_data dd1
                ON pb.serve_type = dd1.dict_value
                AND dd1.dict_type = 'business_plane_serve_type'
            LEFT JOIN sys_dict_data dd2
                ON pb.book_status = dd2.dict_value
                AND dd2.dict_type = 'business_book_status'
            LEFT JOIN sys_dept dept
                ON dept.dept_id = pb.dept_id
        WHERE 1 = 1
            <if test="q.serveType != null and q.serveType != ''">
                AND pb.serve_type = #{q.serveType}
            </if>
            <if test="q.bookStatus != null and q.bookStatus != ''">
                AND pb.book_status = #{q.bookStatus}
            </if>
            <if test="q.bookUserId != null and q.bookUserId != ''">
                AND pb.book_user_id = #{q.bookUserId}
            </if>
            <if test="q.serveId != null and q.serveId != ''">
                AND pb.serve_id = #{q.serveId}
            </if>
            <if test="q.bookNo != null and q.bookNo != ''">
                AND pb.book_no = #{q.bookNo}
            </if>
            <if test="q.expirationDate != null and q.expirationDate != ''">
                AND pb.expiration_date = #{q.expirationDate}
            </if>
            <if test="q.totalPrice != null">
                AND pb.total_price = #{q.totalPrice}
            </if>
            <if test="q.contactName != null and q.contactName != ''">
                AND pb.contact_name LIKE CONCAT(#{q.contactName}, '%')
            </if>
            <if test="q.phone != null and q.phone != ''">
                AND pb.phone LIKE CONCAT(#{q.phone}, '%')
            </if>
            <if test="q.idCard != null and q.idCard != ''">
                AND pb.id_card LIKE CONCAT(#{q.idCard}, '%')
            </if>
            <if test="q.addressDetail != null and q.addressDetail != ''">
                AND pb.address_detail LIKE CONCAT('%', #{q.addressDetail}, '%')
            </if>
            <if test="q.plotCount != null">
                AND pb.plot_count = #{q.plotCount}
            </if>
            <if test="q.area != null">
                AND pb.area = #{q.area}
            </if>
    </select>

    <select id="selectById" resultType="com.shengyu.business.domain.dto.PlaneBookDto">
        SELECT
            <include refid="Base_Column_List"/>,
            dd1.dict_label AS serveTypeName,
            dd2.dict_label AS bookStatusName,
            dept.dept_name AS deptName
        FROM t_business_plane_book pb
            LEFT JOIN sys_dict_data dd1
                ON pb.serve_type = dd1.dict_value
                AND dd1.dict_type = 'business_plane_serve_type'
            LEFT JOIN sys_dict_data dd2
                ON pb.book_status = dd2.dict_value
                AND dd2.dict_type = 'business_book_status'
            LEFT JOIN sys_dept dept
                ON dept.dept_id = pb.dept_id
        WHERE pb.id = #{id}
    </select>

    <select id="selectByIdIgnoreTenant" resultType="com.shengyu.business.domain.dto.PlaneBookDto">
        SELECT
        <include refid="Base_Column_List"/>,
        dd1.dict_label AS serveTypeName,
        dd2.dict_label AS bookStatusName,
        dept.dept_name AS deptName
        FROM t_business_plane_book pb
        LEFT JOIN sys_dict_data dd1
        ON pb.serve_type = dd1.dict_value
        AND dd1.dict_type = 'business_plane_serve_type'
        LEFT JOIN sys_dict_data dd2
        ON pb.book_status = dd2.dict_value
        AND dd2.dict_type = 'business_book_status'
        LEFT JOIN sys_dept dept
        ON dept.dept_id = pb.dept_id
        WHERE pb.id = #{id}
    </select>

    <update id="deleteByIdIgnoreTenant">
        UPDATE t_business_plane_book SET delete_flag = 1 WHERE id = #{id}
    </update>

    <insert id="insertIgnoreTenant" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_business_plane_book
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bookNo != null">book_no,</if>
            <if test="bookUserId != null">book_user_id,</if>
            <if test="serveType != null">serve_type,</if>
            <if test="serveId != null">serve_id,</if>
            <if test="bookStatus != null">book_status,</if>
            <if test="contactName != null">contact_name,</if>
            <if test="phone != null">phone,</if>
            <if test="idCard != null">id_card,</if>
            <if test="plotCount != null">plot_count,</if>
            <if test="area != null">area,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="addressDetail != null">address_detail,</if>
            <if test="unitPrice != null">unit_price,</if>
            <if test="expirationDate != null">expiration_date,</if>
            <if test="totalPrice != null">total_price,</if>
            <if test="deleteFlag != null">delete_flag,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="tenantId != null">tenant_id,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="bookNo != null">#{bookNo},</if>
            <if test="bookUserId != null">#{bookUserId},</if>
            <if test="serveType != null">#{serveType},</if>
            <if test="serveId != null">#{serveId},</if>
            <if test="bookStatus != null">#{bookStatus},</if>
            <if test="contactName != null">#{contactName},</if>
            <if test="phone != null">#{phone},</if>
            <if test="idCard != null">#{idCard},</if>
            <if test="plotCount != null">#{plotCount},</if>
            <if test="area != null">#{area},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="addressDetail != null">#{addressDetail},</if>
            <if test="unitPrice != null">#{unitPrice},</if>
            <if test="expirationDate != null">#{expirationDate},</if>
            <if test="totalPrice != null">#{totalPrice},</if>
            <if test="deleteFlag != null">#{deleteFlag},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="tenantId != null">#{tenantId},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateByIdIgnoreTenant">
        UPDATE t_business_plane_book
        <set>
            <if test="bookNo != null">book_no = #{bookNo},</if>
            <if test="bookUserId != null">book_user_id = #{bookUserId},</if>
            <if test="serveType != null">serve_type = #{serveType},</if>
            <if test="serveId != null">serve_id = #{serveId},</if>
            <if test="bookStatus != null">book_status = #{bookStatus},</if>
            <if test="contactName != null">contact_name = #{contactName},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="idCard != null">id_card = #{idCard},</if>
            <if test="plotCount != null">plot_count = #{plotCount},</if>
            <if test="area != null">area = #{area},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="addressDetail != null">address_detail = #{addressDetail},</if>
            <if test="unitPrice != null">unit_price = #{unitPrice},</if>
            <if test="expirationDate != null">expiration_date = #{expirationDate},</if>
            <if test="totalPrice != null">total_price = #{totalPrice},</if>
            <if test="deleteFlag != null">delete_flag = #{deleteFlag},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </set>
        WHERE id = #{id}
    </update>

</mapper>