package com.shengyu.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.shengyu.business.domain.PlaneServe;
import com.shengyu.business.domain.dto.PlaneServeDto;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import org.apache.ibatis.annotations.Param;

/**
 * 飞防服务表 Mapper接口
 *
 * <AUTHOR>
 * @since 2025-07-12 17:34:46
 */
public interface BusinessPlaneServeMapper extends BaseMapper<PlaneServe> {

    // ====================================================================================
    // =================================== manage =========================================
    // ====================================================================================

    /**
     * 分页查询飞防服务列表（租户隔离）
     *
     * @param page       分页参数
     * @param planeServe 查询条件
     * @return 分页结果
     */
    IPage<PlaneServeDto> selectPage(Page<PlaneServeDto> page, @Param("q") PlaneServe planeServe);

    /**
     * 根据ID查询飞防服务（租户隔离）
     *
     * @param id 飞防服务ID
     * @return 飞防服务信息，未找到返回null
     */
    PlaneServe selectById(Long id);

    /**
     * 根据服务类型查询（租户隔离，仅上架）
     *
     * @param serveType 服务类型编码
     * @return 飞防服务信息，未找到返回null
     */
    PlaneServe selectByServeType(String serveType);

    // ====================================================================================
    // ===================================== app ==========================================
    // ====================================================================================

    /**
     * 根据服务类型查询（忽略租户，仅上架）
     *
     * @param serveType 服务类型编码
     * @return 飞防服务信息，未找到返回null
     */
    @InterceptorIgnore(tenantLine = "true")
    PlaneServe selectByServeTypeIgnoreTenant(@Param("serveType") String serveType);

    /**
     * 根据ID查询（忽略租户）
     *
     * @param id 飞防服务ID
     * @return 飞防服务信息，未找到返回null
     */
    @InterceptorIgnore(tenantLine = "true")
    PlaneServe selectByIdIgnoreTenant(@Param("id") Long id);
}
