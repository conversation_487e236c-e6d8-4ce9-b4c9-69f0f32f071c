package com.shengyu.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.shengyu.business.domain.CustodyBook;
import com.shengyu.business.domain.dto.CustodyBookDto;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import org.apache.ibatis.annotations.Param;

/**
 * 托管预定表数据库操作Mapper
 *
 * <AUTHOR>
 * @description 针对表【t_custody_book(托管预定表)】的数据库操作Mapper
 * @createDate 2025-07-12 15:22:06
 * @Entity com.shengyu.base.domain.CustodyBook
 */
public interface BusinessCustodyBookMapper extends BaseMapper<CustodyBook> {

    // ====================================================================================
    // =================================== manage =========================================
    // ====================================================================================

    /**
     * 分页查询托管预定列表（返回DTO）
     */
    IPage<CustodyBookDto> selectPage(Page<CustodyBookDto> page, @Param("q") CustodyBook book);

    /**
     * 分页查询托管预定列表（返回DTO） - 忽略租户
     */
    @InterceptorIgnore(tenantLine = "true")
    IPage<CustodyBookDto> selectPageIgnoreTenant(Page<CustodyBookDto> page, @Param("q") CustodyBook book);

    // ====================================================================================
    // ===================================== app ==========================================
    // ====================================================================================

    /**
     * 根据ID查询托管预定记录
     *
     * @param id 托管预定记录ID
     * @return 托管预定记录对象
     */
    CustodyBookDto selectCustodyBookById(@Param("id") Long id);

    /**
     * 根据ID查询托管预定记录 - 忽略租户
     *
     * @param id 托管预定记录ID
     * @return 托管预定记录对象
     */
    @InterceptorIgnore(tenantLine = "true")
    CustodyBookDto selectCustodyBookByIdIgnoreTenant(@Param("id") Long id);

    /**
     * 插入托管预定记录 - 忽略租户（用于需要手动指定 tenantId 的场景）
     */
    @InterceptorIgnore(tenantLine = "true")
    int insertWithServeTenantId(CustodyBook entity);

    /**
     * 更新托管预定记录 - 忽略租户（用于需要手动指定或保持 tenantId 的场景）
     */
    @InterceptorIgnore(tenantLine = "true")
    int updateByIdWithServeTenantId(CustodyBook entity);

    /**
     * 根据ID删除托管预定记录 - 忽略租户
     */
    @InterceptorIgnore(tenantLine = "true")
    int deleteByIdIgnoreTenant(@Param("id") Long id);
}
