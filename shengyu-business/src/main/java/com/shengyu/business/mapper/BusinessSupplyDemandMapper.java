package com.shengyu.business.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.shengyu.business.domain.SupplyDemand;
import com.shengyu.business.domain.dto.SupplyDemandDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 供需信息表数据访问层接口
 *
 * <AUTHOR>
 * @description 针对表【t_business_supply_demand(供需信息表)】的数据库操作Mapper
 * @createDate 2025-07-16 14:32:12
 * @Entity com.shengyu.business.domain.BusinessSupplyDemand
 */
public interface BusinessSupplyDemandMapper extends BaseMapper<SupplyDemand> {

    // ====================================================================================
    // =================================== manage =========================================
    // ====================================================================================

    /**
     * 分页查询供需信息列表（MyBatis-Plus分页）
     *
     * @param page 分页参数
     * @param supplyDemand 查询条件
     * @return 分页结果
     */
    IPage<SupplyDemandDto> getPage(Page<SupplyDemandDto> page, @Param("sd") SupplyDemand supplyDemand);

    /**
     * 查询供需信息列表
     *
     * @param supplyDemand 查询条件
     * @return 供需信息列表
     */
    List<SupplyDemandDto> getList(@Param("sd") SupplyDemand supplyDemand);

    /**
     * 根据ID查询供需信息详情
     *
     * @param id 供需信息ID
     * @return 供需信息DTO对象
     */
    SupplyDemandDto selectById(@Param("id") Long id);

    /**
     * 保存供需信息
     *
     * @param entity 待保存的供需信息对象
     * @return 插入结果，返回插入的行数
     */
    int updateById(@Param("entity") SupplyDemand entity);

    /**
     * 根据ID查询有效的供需信息
     *
     * @param id 供需信息ID
     * @return 有效的供需信息DTO对象
     */
    SupplyDemandDto selectValidById(@Param("id") Long id);

    // ====================================================================================
    // ===================================== app ==========================================
    // ====================================================================================

    /**
     * 分页查询供需信息列表（忽略租户）
     *
     * @param page 分页参数
     * @param supplyDemand 查询条件
     * @return 分页结果
     */
    @InterceptorIgnore(tenantLine = "true")
    IPage<SupplyDemandDto> getPageIgnoreTenant(Page<SupplyDemandDto> page, @Param("sd") SupplyDemand supplyDemand);

    /**
     * 根据ID查询供需信息详情（忽略租户）
     *
     * @param id 供需信息ID
     * @return 供需信息DTO对象
     */
    @InterceptorIgnore(tenantLine = "true")
    SupplyDemandDto selectByIdIgnoreTenant(@Param("id") Long id);

    /**
     * 根据ID查询有效的供需信息（忽略租户）
     *
     * @param id 供需信息ID
     * @return 有效的供需信息DTO对象
     */
    @InterceptorIgnore(tenantLine = "true")
    SupplyDemandDto selectValidByIdIgnoreTenant(@Param("id") Long id);

    /**
     * 保存供需信息（忽略租户）
     *
     * @param supplyDemand 供需信息对象
     * @return 影响行数
     */
    @InterceptorIgnore(tenantLine = "true")
    int saveIgnoreTenant(@Param("entity") SupplyDemand supplyDemand);

    /**
     * 根据ID更新供需信息（忽略租户）
     *
     * @param supplyDemand 供需信息对象
     * @return 影响行数
     */
    @InterceptorIgnore(tenantLine = "true")
    int updateByIdIgnoreTenant(@Param("entity") SupplyDemand supplyDemand);

    /**
     * 根据ID删除供需信息（忽略租户）
     *
     * @param id 供需信息ID
     * @return 影响行数
     */
    @InterceptorIgnore(tenantLine = "true")
    int removeByIdIgnoreTenant(@Param("id") Long id);
}
