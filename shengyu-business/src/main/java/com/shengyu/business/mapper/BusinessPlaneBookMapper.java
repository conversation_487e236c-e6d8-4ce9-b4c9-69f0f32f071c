package com.shengyu.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.shengyu.business.domain.PlaneBook;
import com.shengyu.business.domain.dto.PlaneBookDto;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 飞防服务预定 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-07-12 17:34:46
 */
@Mapper
public interface BusinessPlaneBookMapper extends BaseMapper<PlaneBook> {

    // ====================================================================================
    // =================================== manage =========================================
    // ====================================================================================

    /**
     * 分页查询飞防服务预定列表（租户隔离）
     *
     * @param page 分页参数
     * @param book 查询条件
     * @return 分页结果
     */
    IPage<PlaneBookDto> selectPage(@Param("page") Page<PlaneBookDto> page, @Param("q") PlaneBook book);

    // ====================================================================================
    // ===================================== app ==========================================
    // ====================================================================================

    /**
     * 分页查询飞防服务预定列表（忽略租户）
     *
     * @param page 分页参数
     * @param book 查询条件
     * @return 分页结果
     */
    @InterceptorIgnore(tenantLine = "true")
    IPage<PlaneBookDto> selectPageIgnoreTenant(@Param("page") Page<PlaneBookDto> page, @Param("q") PlaneBook book);

    /**
     * 根据ID查询预定详情（忽略租户）
     *
     * @param id 飞防服务预定ID
     * @return 飞防服务预定信息，未找到返回null
     */
    @InterceptorIgnore(tenantLine = "true")
    PlaneBook selectByIdIgnoreTenant(@Param("id") Long id);

    /**
     * 插入预定信息（忽略租户）
     *
     * @param planeBook 预定信息
     * @return 影响行数
     */
    @InterceptorIgnore(tenantLine = "true")
    int insertIgnoreTenant(PlaneBook planeBook);

    /**
     * 更新预定信息（忽略租户）
     *
     * @param planeBook 预定信息
     * @return 影响行数
     */
    @InterceptorIgnore(tenantLine = "true")
    int updateByIdIgnoreTenant(PlaneBook planeBook);

    /**
     * 根据ID逻辑删除（忽略租户）
     *
     * @param id 飞防服务预定ID
     * @return 影响行数
     */
    @InterceptorIgnore(tenantLine = "true")
    int deleteByIdIgnoreTenant(@Param("id") Long id);
}
