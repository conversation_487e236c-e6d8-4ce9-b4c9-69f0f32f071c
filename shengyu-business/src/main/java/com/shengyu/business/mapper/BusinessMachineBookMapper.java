package com.shengyu.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.shengyu.business.domain.MachineBook;
import com.shengyu.business.domain.dto.MachineBookDto;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @description 针对表【t_business_machine_book(农机服务预定表)】的数据库操作Mapper
 * @createDate 2025-07-12 17:34:46
 * @Entity com.shengyu.base.domain.BusinessMachineBook
 */
public interface BusinessMachineBookMapper extends BaseMapper<MachineBook> {

    // ====================================================================================
    // =================================== manage =========================================
    // ====================================================================================

    /**
     * 分页查询农机服务预定列表（返回DTO）
     *
     * @param page 分页参数
     * @param book 查询条件
     * @return 分页结果
     */
    IPage<MachineBookDto> selectPage(Page<MachineBookDto> page, @Param("q") MachineBook book);

    // ====================================================================================
    // ===================================== app ==========================================
    // ====================================================================================

    /**
     * 分页查询农机服务预定列表（返回DTO）- 忽略租户
     */
    @InterceptorIgnore(tenantLine = "true")
    IPage<MachineBookDto> selectPageIgnoreTenant(Page<MachineBookDto> page, @Param("q") MachineBook book);

    /**
     * 根据ID查询预定（实体）- 忽略租户
     */
    @InterceptorIgnore(tenantLine = "true")
    MachineBookDto selectByIdIgnoreTenant(@Param("id") Long id);

    /**
     * 新增预定（实体）- 忽略租户
     */
    @InterceptorIgnore(tenantLine = "true")
    int insertIgnoreTenant(@Param("et") MachineBook entity);

    /**
     * 根据ID更新（实体）- 忽略租户
     */
    @InterceptorIgnore(tenantLine = "true")
    int updateByIdIgnoreTenant(@Param("et") MachineBook entity);

    /**
     * 根据ID删除（实体）- 忽略租户
     */
    @InterceptorIgnore(tenantLine = "true")
    int deleteByIdIgnoreTenant(@Param("id") Long id);
}




