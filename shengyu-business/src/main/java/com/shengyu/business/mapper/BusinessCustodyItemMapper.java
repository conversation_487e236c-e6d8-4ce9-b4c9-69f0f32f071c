package com.shengyu.business.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shengyu.business.domain.CustodyItem;
import com.shengyu.business.domain.dto.CustodyItemDto;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * 托管服务作业项Mapper
 */
public interface BusinessCustodyItemMapper extends BaseMapper<CustodyItem> {

    // ====================================================================================
    // =================================== manage =========================================
    // ====================================================================================

    /**
     * 分页查询托管服务作业项（返回DTO）
     *
     * @param page 分页
     * @param custodyItem 条件
     * @return 分页结果
     */
    IPage<CustodyItemDto> selectPage(Page<CustodyItemDto> page, @Param("q") CustodyItem custodyItem);

    /**
     * 根据ID查询作业项
     *
     * @param id 作业项ID
     * @return 作业项
     */
    CustodyItemDto selectById(@Param("id") Long id);

    /**
     * 根据ID列表查询作业项
     *
     * @param ids 作业项ID列表
     * @return 作业项集合
     */
    List<CustodyItemDto> selectList(@Param("ids") List<Long> ids);

    // ====================================================================================
    // ===================================== app ==========================================
    // ====================================================================================

    /**
     * 根据ID查询作业项 - 忽略租户
     *
     * @param id 作业项ID
     * @return 作业项
     */
    @InterceptorIgnore(tenantLine = "true")
    CustodyItemDto selectByIdIgnoreTenant(@Param("id") Long id);

    /**
     * 根据ID列表查询作业项 - 忽略租户
     *
     * @param ids 作业项ID列表
     * @return 作业项集合
     */
    @InterceptorIgnore(tenantLine = "true")
    List<CustodyItemDto> selectListIgnoreTenant(@Param("ids") List<Long> ids);
}