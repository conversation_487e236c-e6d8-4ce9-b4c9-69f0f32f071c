package com.shengyu.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.shengyu.business.domain.CustodyServe;
import com.shengyu.business.domain.dto.CustodyServeDto;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import org.apache.ibatis.annotations.Param;

/**
 * 托管Mapper接口
 * 提供对托管数据访问的定义，包括增删改查操作
 */
public interface BusinessCustodyServeMapper extends BaseMapper<CustodyServe> {

    // ====================================================================================
    // =================================== manage =========================================
    // ====================================================================================

    /**
     * 分页查询托管列表（返回DTO）
     */
    IPage<CustodyServeDto> selectPage(Page<CustodyServeDto> page, @Param("q") CustodyServeDto custodyServe);

    /**
     * 根据ID查询托管信息
     *
     * @param id 托管ID
     * @return 符合条件的托管对象
     */
    CustodyServeDto selectById(@Param("id") Long id);

    // ====================================================================================
    // ===================================== app ==========================================
    // ====================================================================================

    /**
     * 分页查询托管列表（返回DTO）- 忽略租户
     */
    @InterceptorIgnore(tenantLine = "true")
    IPage<CustodyServeDto> selectPageIgnoreTenant(Page<CustodyServeDto> page, @Param("q") CustodyServeDto custodyServe);

    /**
     * 根据ID查询托管信息
     *
     * @param id 托管ID
     * @return 符合条件的托管对象
     */
    @InterceptorIgnore(tenantLine = "true")
    CustodyServeDto selectByIdIgnoreTenant(@Param("id") Long id);

    /**
     * 托管作业项是否包含在托管中
     *
     * @param singleId 服务项id
     * @return 包含记录数
     */
    Integer itemIdIsInServe(@Param("singleId") Long singleId);
}