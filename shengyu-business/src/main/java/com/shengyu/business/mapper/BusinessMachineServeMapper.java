package com.shengyu.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.shengyu.business.domain.MachineServe;
import com.shengyu.business.domain.dto.MachineServeDto;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @description 针对表【t_business_machine_serve(农机服务表)】的数据库操作Mapper
 * @createDate 2025-07-12 17:34:46
 * @Entity com.shengyu.base.domain.BusinessMachineServe
 */
public interface BusinessMachineServeMapper extends BaseMapper<MachineServe> {

    // ====================================================================================
    // =================================== manage =========================================
    // ====================================================================================

    /**
     * 分页查询农机服务列表（返回DTO）
     *
     * @param page 分页参数
     * @param machineServe 查询条件
     * @return 分页结果
     */
    IPage<MachineServeDto> selectPage(Page<MachineServeDto> page, @Param("q") MachineServe machineServe);

    /**
     * 根据ID查询农机服务详情
     *
     * @param id 农机服务ID
     * @return 农机服务详情
     */
    MachineServe selectById(Long id);

    // ====================================================================================
    // ===================================== app ==========================================
    // ====================================================================================

    /**
     * 根据服务类型查询单个农机服务（上架且未删除）- 忽略租户
     *
     * @param serveType 服务类型编码
     * @return 农机服务实体
     */
    @InterceptorIgnore(tenantLine = "true")
    MachineServe selectByServeTypeIgnoreTenant(String serveType);
}
