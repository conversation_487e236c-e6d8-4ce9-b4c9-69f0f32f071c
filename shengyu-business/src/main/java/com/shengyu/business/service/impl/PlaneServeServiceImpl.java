package com.shengyu.business.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dxy.library.snowflake.SnowflakeIdUtils;
import com.shengyu.business.domain.PlaneServe;
import com.shengyu.business.mapper.BusinessPlaneServeMapper;
import com.shengyu.business.service.IPlaneServeService;
import com.shengyu.common.exception.CustomException;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.shengyu.business.domain.dto.PlaneServeDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

/**
 * 飞防服务表 Service实现
 *
 * <AUTHOR>
 * @since 2025-07-12 17:34:46
 */
@Service
public class PlaneServeServiceImpl extends ServiceImpl<BusinessPlaneServeMapper, PlaneServe>
        implements IPlaneServeService {

    // ====================================================================================
    // =================================== manage =========================================
    // ====================================================================================

    @Override
    public IPage<PlaneServeDto> selectPage(Page<PlaneServeDto> page, PlaneServe planeServe) {
        return baseMapper.selectPage(page, planeServe);
    }

    @Override
    public PlaneServe selectById(Long id) {
        return this.getById(id);
    }

    @Override
    @Transactional
    public boolean savePlaneServe(PlaneServe planeServe) {
        planeServe.setServeNo("MS" + SnowflakeIdUtils.getAsLong());
        return super.save(planeServe);
    }

    @Override
    @Transactional
    public boolean updatePlaneServe(PlaneServe planeServe) {
        PlaneServe oldPlaneServe = selectById(planeServe.getId());
        if (Objects.isNull(oldPlaneServe)) {
            throw new CustomException("飞防服务不存在");
        }
        if (Objects.equals(oldPlaneServe.getStatus(), 1)) {
            throw new CustomException("该操作项已上架，请先下架再编辑!");
        }
        return this.updateById(planeServe);
    }

    @Override
    @Transactional
    public boolean removeByIds(List<Long> ids) {
        for (Long id : ids) {
            PlaneServe planeServe = this.getById(id);
            if (Objects.isNull(planeServe)) {
                throw new CustomException("该操作项已不存在!");
            }
            if (Objects.equals(planeServe.getStatus(), 1)) {
                throw new CustomException("该操作项已上架，请先下架再删除!");
            }
        }
        baseMapper.deleteBatchIds(ids);
        return true;
    }

    // ====================================================================================
    // ===================================== app ==========================================
    // ====================================================================================

    @Override
    public PlaneServe selectByServeTypeIgnoreTenant(String serveType) {
        return baseMapper.selectByServeTypeIgnoreTenant(serveType);
    }

    @Override
    public PlaneServe selectByIdIgnoreTenant(Long id) {
        return baseMapper.selectByIdIgnoreTenant(id);
    }

}
