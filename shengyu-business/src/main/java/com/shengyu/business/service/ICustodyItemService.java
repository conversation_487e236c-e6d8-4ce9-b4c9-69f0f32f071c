package com.shengyu.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.shengyu.business.domain.CustodyItem;
import com.shengyu.business.domain.dto.CustodyItemDto;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * 托管服务作业项服务层接口
 */
public interface ICustodyItemService extends IService<CustodyItem> {

    // ====================================================================================
    // =================================== manage =========================================
    // ====================================================================================

    /**
     * 分页查询托管服务作业项
     *
     * @param page        分页参数
     * @param custodyItem 查询条件
     * @return 分页结果
     */
    IPage<CustodyItemDto> selectPage(Page<CustodyItemDto> page, CustodyItem custodyItem);

    /**
     * 获取指定ID的托管服务作业项对象
     *
     * @param id 托管服务作业项ID
     * @return 符合指定ID的托管服务作业项对象
     */
    CustodyItemDto selectById(Long id);

    /**
     * 新增托管服务作业项
     *
     * @param custodyItem 托管服务作业项
     * @return 新增结果
     */
    boolean saveCustodyItem(CustodyItem custodyItem);

    /**
     * 修改托管服务作业项
     *
     * @param custodyItem 托管服务作业项
     * @return 修改结果
     */
    boolean updateCustodyItem(CustodyItem custodyItem);

    /**
     * 根据ID列表删除托管服务作业项对象
     *
     * @param ids 托管服务作业项ID列表
     * @return 删除成功返回true，否则返回false
     */
    boolean removeByIds(List<Long> ids);

    /**
     * 根据ID列表查询托管服务作业项对象
     *
     * @param ids 托管服务作业项ID列表
     * @return 符合指定ID的托管服务作业项对象集合
     */
    List<CustodyItemDto> selectList(List<Long> ids);

    // ====================================================================================
    // ===================================== app ==========================================
    // ====================================================================================

    /**
     * 根据ID列表查询托管服务作业项对象 - 忽略租户
     *
     * @param ids 托管服务作业项ID列表
     * @return 符合指定ID的托管服务作业项对象集合
     */
    List<CustodyItemDto> selectListIgnoreTenant(List<Long> ids);

    /**
     * 获取指定ID的托管服务作业项对象 - 忽略租户
     *
     * @param id 托管服务作业项ID
     * @return 符合指定ID的托管服务作业项对象
     */
    CustodyItemDto selectByIdIgnoreTenant(Long id);

    // ====================================================================================
    // =================================== shared util ====================================
    // ====================================================================================

    /**
     * 工具方法：根据以逗号分隔的ID字符串加载作业项列表
     *
     * @param itemIdStr  逗号分隔的ID字符串，如 "1,2,3"
     * @param ignoreTenant 是否忽略租户
     * @return 作业项列表（若入参为空则返回空列表）
     */
    List<CustodyItemDto> loadItemsByIdString(String itemIdStr, boolean ignoreTenant);
}