package com.shengyu.business.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dxy.library.snowflake.SnowflakeIdUtils;
import com.shengyu.business.domain.MachineBook;
import com.shengyu.business.domain.MachineServe;
import com.shengyu.business.domain.dto.MachineBookDto;
import com.shengyu.business.mapper.BusinessMachineBookMapper;
import com.shengyu.business.service.IMachineBookService;
import com.shengyu.business.service.IMachineServeService;
import com.shengyu.common.exception.CustomException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 针对表【t_business_machine_book(农机服务预定表)】的数据库操作Service实现
 * @createDate 2025-07-12 17:34:46
 */
@Service
public class MachineBookServiceImpl extends ServiceImpl<BusinessMachineBookMapper, MachineBook>
        implements IMachineBookService {

    @Autowired
    private IMachineServeService machineServeService;

    // ====================================================================================
    // =================================== manage =========================================
    // ====================================================================================

    @Override
    public IPage<MachineBookDto> selectPage(Page<MachineBookDto> page, MachineBook book) {
        return baseMapper.selectPage(page, book);
    }

    @Override
    public MachineBook selectById(Long id) {
        return this.getById(id);
    }

    

    // ====================================================================================
    // ===================================== app ==========================================
    // ====================================================================================

    @Override
    public IPage<MachineBookDto> selectPageIgnoreTenant(Page<MachineBookDto> page, MachineBook book) {
        return baseMapper.selectPageIgnoreTenant(page, book);
    }

    @Override
    public MachineBook selectByIdUserIdIgnoreTenant(Long id, Long userId) {
        MachineBook book = baseMapper.selectByIdIgnoreTenant(id);
        if (book == null || !userId.equals(book.getBookUserId())) {
            throw new CustomException("无权限操作");
        }
        return book;
    }

    @Override
    @Transactional
    public boolean saveMachineBookIgnoreTenant(MachineBook machineBook) {
        // 使用忽略租户的方式查询 serve 信息
        MachineServe serve = machineServeService.selectByServeTypeIgnoreTenant(machineBook.getServeType());
        if (serve == null) {
            throw new CustomException("农机服务不存在");
        }
        // 规则：整型比较使用 Objects.equals，避免 NPE
        if (Objects.equals(serve.getStatus(), 0) || Objects.equals(serve.getDeleteFlag(), 1)) {
            throw new CustomException("农机服务已下架或不存在");
        }
        
        // 设置 book 的租户 ID 为 serve 的租户 ID
        machineBook.setTenantId(serve.getTenantId());
        machineBook.setServeId(serve.getId());
        machineBook.setBookNo("MB" + SnowflakeIdUtils.getAsString());
        return baseMapper.insertIgnoreTenant(machineBook) > 0;
    }

    @Override
    @Transactional
    public boolean updateByIdIgnoreTenant(MachineBook machineBook, Long userId) {
        MachineBook existingBook = selectByIdUserIdIgnoreTenant(machineBook.getId(), userId);
        if (!"0".equals(existingBook.getBookStatus())) {
            throw new CustomException("当前状态无法修改");
        }

        MachineServe serve = machineServeService.selectByServeTypeIgnoreTenant(machineBook.getServeType());
        if (serve == null) {
            throw new CustomException("农机服务不存在");
        }
        if (Objects.equals(serve.getStatus(), 0) || Objects.equals(serve.getDeleteFlag(), 1)) {
            throw new CustomException("农机服务已下架或不存在");
        }
        machineBook.setTenantId(serve.getTenantId());
        machineBook.setServeId(serve.getId());

        return baseMapper.updateByIdIgnoreTenant(machineBook) > 0;
    }

    @Override
    @Transactional
    public boolean removeByIdIgnoreTenant(Long id, Long userId) {
        selectByIdUserIdIgnoreTenant(id, userId);
        return baseMapper.deleteByIdIgnoreTenant(id) > 0;
    }
}
