package com.shengyu.business.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shengyu.business.domain.CustodyServe;
import com.shengyu.business.domain.dto.CustodyItemDto;
import com.shengyu.business.domain.dto.CustodyServeDto;
import com.shengyu.business.mapper.BusinessCustodyServeMapper;
import com.shengyu.business.service.ICustodyItemService;
import com.shengyu.business.service.ICustodyServeService;
import com.shengyu.common.core.text.Convert;
import com.shengyu.common.exception.CustomException;
import com.shengyu.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

@Service
public class CustodyServeServiceImpl
        extends ServiceImpl<BusinessCustodyServeMapper, CustodyServe> implements ICustodyServeService {

    @Autowired
    private ICustodyItemService custodyItemService;

    // ====================================================================================
    // =================================== manage =========================================
    // ====================================================================================

    @Override
    public IPage<CustodyServeDto> selectPage(Page<CustodyServeDto> page, CustodyServeDto custodyServeDto) {
        IPage<CustodyServeDto> ipage = baseMapper.selectPage(page, custodyServeDto);
        enrichCustodyServeDtos(ipage.getRecords(), false);
        return ipage;
    }

    @Override
    public CustodyServeDto selectById(Long id) {
        CustodyServeDto dto = baseMapper.selectById(id);
        if (dto != null && !StringUtils.isEmpty(dto.getServiceItems())) {
            dto.setItems(custodyItemService.loadItemsByIdString(dto.getServiceItems(), false));
        }
        return dto;
    }

    @Override
    @Transactional
    public boolean saveCustodyServe(CustodyServe custodyServe) {
        // 计算价格与补贴
        fillAmountsFromItems(custodyServe);
        return baseMapper.insert(custodyServe) > 0;
    }

    @Override
    @Transactional
    public boolean updateCustodyServe(CustodyServe custodyServe) {
        CustodyServe oldCustodyServe = this.getById(custodyServe.getId());
        if (oldCustodyServe.getStatus().equals(1)) {
            throw new CustomException("该操作项已上架，请先下架再编辑!");
        }
        // 计算价格与补贴
        fillAmountsFromItems(custodyServe);
        return baseMapper.updateById(custodyServe) > 0;
    }

    @Override
    @Transactional
    public boolean removeByIds(List<Long> ids) {
        for (Long id : ids) {
            CustodyServe custodyServe = getById(id);
            if (custodyServe.getStatus().equals(1)) {
                throw new CustomException("该操作项已上架，请先下架再删除!");
            }
        }
        baseMapper.deleteBatchIds(ids);
        return true;
    }

    // ====================================================================================
    // ===================================== app ==========================================
    // ====================================================================================

    @Override
    public IPage<CustodyServeDto> selectPageIgnoreTenant(Page<CustodyServeDto> page, CustodyServeDto custodyServeDto) {
        IPage<CustodyServeDto> ipage = baseMapper.selectPageIgnoreTenant(page, custodyServeDto);
        enrichCustodyServeDtos(ipage.getRecords(), true);
        return ipage;
    }

    @Override
    public CustodyServeDto selectByIdIgnoreTenant(Long id) {
        CustodyServeDto dto = baseMapper.selectByIdIgnoreTenant(id);
        if (dto != null && !StringUtils.isEmpty(dto.getServiceItems())) {
            dto.setItems(custodyItemService.loadItemsByIdString(dto.getServiceItems(), true));
        }
        return dto;
    }

    // ====================================================================================
    // =================================== private ========================================
    // ====================================================================================

    private void enrichCustodyServeDtos(List<CustodyServeDto> records, boolean ignoreTenant) {
        if (records == null || records.isEmpty()) {
            return;
        }
        for (CustodyServeDto record : records) {
            if (!StringUtils.isEmpty(record.getServiceItems())) {
                record.setItems(custodyItemService.loadItemsByIdString(record.getServiceItems(), ignoreTenant));
            }
        }
    }

    private void fillAmountsFromItems(CustodyServe custodyServe) {
        BigDecimal totalPrice = BigDecimal.ZERO;
        BigDecimal totalSubsidy = BigDecimal.ZERO;
        String itemIdStr = custodyServe.getServiceItems();
        if (!StringUtils.isEmpty(itemIdStr)) {
            List<Long> itemIds = Arrays.asList(Convert.toLongArray(itemIdStr));
            if (!itemIds.isEmpty()) {
                List<CustodyItemDto> items = custodyItemService.selectList(itemIds);
                if (items != null) {
                    for (CustodyItemDto item : items) {
                        BigDecimal unitPrice = item.getUnitPrice() == null ? BigDecimal.ZERO : item.getUnitPrice();
                        BigDecimal subsidy = item.getSubsidy() == null ? BigDecimal.ZERO : item.getSubsidy();
                        totalPrice = totalPrice.add(unitPrice);
                        totalSubsidy = totalSubsidy.add(subsidy);
                    }
                }
            }
        }
        custodyServe.setUnitPrice(totalPrice);
        custodyServe.setSubsidy(totalSubsidy);
    }

}