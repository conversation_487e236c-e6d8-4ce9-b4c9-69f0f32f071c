package com.shengyu.business.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.shengyu.business.domain.SupplyDemand;
import com.shengyu.business.domain.dto.SupplyDemandDto;
import com.shengyu.business.mapper.BusinessSupplyDemandMapper;
import com.shengyu.business.service.ISupplyDemandService;
import com.shengyu.common.exception.CustomException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【t_business_supply_demand(供需信息表)】的数据库操作Service实现
 * @createDate 2025-07-16 14:32:12
 */
@Service
public class SupplyDemandServiceImpl extends ServiceImpl<BusinessSupplyDemandMapper, SupplyDemand>
        implements ISupplyDemandService {

    // ====================================================================================
    // =================================== manage =========================================
    // ====================================================================================

    @Override
    public IPage<SupplyDemandDto> selectPage(Page<SupplyDemandDto> page, SupplyDemand supplyDemand) {
        return baseMapper.getPage(page, supplyDemand);
    }

    @Override
    public List<SupplyDemandDto> selectList(SupplyDemand supplyDemand) {
        return baseMapper.getList(supplyDemand);
    }

    @Override
    public SupplyDemandDto selectOne(Long id) {
        return baseMapper.selectById(id);
    }

    // ====================================================================================
    // ===================================== app ==========================================
    // ====================================================================================

    @Override
    public IPage<SupplyDemandDto> selectPageIgnoreTenant(Page<SupplyDemandDto> page, SupplyDemand supplyDemand) {
        return baseMapper.getPageIgnoreTenant(page, supplyDemand);
    }

    @Override
    public SupplyDemandDto selectValidByIdIgnoreTenant(Long id) {
        return baseMapper.selectValidByIdIgnoreTenant(id);
    }

    @Override
    public SupplyDemandDto selectByIdUserIdIgnoreTenant(Long id, Long userId) {
        SupplyDemandDto entity = baseMapper.selectByIdIgnoreTenant(id);
        if (null == entity) {
            throw new CustomException("供需信息不存在");
        }
        if (!entity.getUserId().equals(userId)) {
            throw new CustomException("无权限操作");
        }
        return entity;
    }

    @Override
    @Transactional
    public boolean saveSupplyDemandIgnoreTenant(SupplyDemand supplyDemand) {
        return baseMapper.saveIgnoreTenant(supplyDemand) > 0;
    }

    @Override
    @Transactional
    public boolean updateByIdUserIdIgnoreTenant(SupplyDemand supplyDemand, Long userId) {
        if (supplyDemand == null || supplyDemand.getId() == null) {
            throw new CustomException("参数不能为空");
        }
        selectByIdUserIdIgnoreTenant(supplyDemand.getId(), userId);
        return baseMapper.updateByIdIgnoreTenant(supplyDemand) > 0;
    }

    @Override
    @Transactional
    public boolean removeByIdUserIdIgnoreTenant(Long id, Long userId) {
        selectByIdUserIdIgnoreTenant(id, userId);
        return baseMapper.removeByIdIgnoreTenant(id) > 0;
    }
}
