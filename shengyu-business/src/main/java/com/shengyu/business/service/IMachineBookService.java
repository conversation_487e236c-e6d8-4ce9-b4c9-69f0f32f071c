package com.shengyu.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.shengyu.business.domain.MachineBook;
import com.shengyu.business.domain.dto.MachineBookDto;

/**
 * <AUTHOR>
 * @description 针对表【t_business_machine_book(农机服务预定表)】的数据库操作Service
 * @createDate 2025-07-12 17:34:46
 */
public interface IMachineBookService extends IService<MachineBook> {

    // ====================================================================================
    // =================================== manage =========================================
    // ====================================================================================

    /**
     * 分页查询农机服务预定列表（返回DTO）
     */
    IPage<MachineBookDto> selectPage(Page<MachineBookDto> page, MachineBook book);

    /**
     * 获取指定ID的农机服务预定对象
     *
     * @param id 农机服务预定ID
     * @return 符合指定ID的农机服务预定对象
     */
    MachineBook selectById(Long id);

    /**
     * 根据ID和用户ID查询农机服务预定
     *
     * @param id     农机服务预定ID
     * @param userId 用户ID
     * @return 符合条件的农机服务预定对象
     */
    // 删除未使用的 manage 方法：selectByIdUserId/saveMachineBook/updateById/removeById

    // ====================================================================================
    // ===================================== app ==========================================
    // ====================================================================================

    /**
     * 分页查询农机服务预定列表（返回DTO）- 忽略租户
     */
    IPage<MachineBookDto> selectPageIgnoreTenant(Page<MachineBookDto> page, MachineBook book);

    /**
     * 根据ID和用户ID查询农机服务预定 - 忽略租户
     *
     * @param id     农机服务预定ID
     * @param userId 用户ID
     * @return 符合条件的农机服务预定对象
     */
    MachineBookDto selectByIdUserIdIgnoreTenant(Long id, Long userId);

    /**
     * 保存农机服务预定 - 忽略租户
     *
     * @param machineBook 待保存的农机服务预定对象
     * @return 是否保存成功
     */
    boolean saveMachineBookIgnoreTenant(MachineBook machineBook);

    /**
     * 用户校验后更新 - 忽略租户
     *
     * @param machineBook 待更新的农机服务预定对象
     * @param userId      用户ID
     * @return 是否更新成功
     */
    boolean updateByIdIgnoreTenant(MachineBook machineBook, Long userId);

    /**
     * 用户校验后删除 - 忽略租户
     *
     * @param id     农机服务预定ID
     * @param userId 用户ID
     * @return 是否删除成功
     */
    boolean removeByIdIgnoreTenant(Long id, Long userId);
}

