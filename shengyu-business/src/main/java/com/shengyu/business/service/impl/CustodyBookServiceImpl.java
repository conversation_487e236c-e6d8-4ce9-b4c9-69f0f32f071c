package com.shengyu.business.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dxy.library.snowflake.SnowflakeIdUtils;
import com.shengyu.business.domain.CustodyBook;
import com.shengyu.business.domain.dto.CustodyBookDto;
import com.shengyu.business.domain.dto.CustodyItemDto;
import com.shengyu.business.domain.dto.CustodyServeDto;
import com.shengyu.business.mapper.BusinessCustodyBookMapper;
import com.shengyu.business.service.ICustodyBookService;
import com.shengyu.business.service.ICustodyItemService;
import com.shengyu.business.service.ICustodyServeService;
import com.shengyu.common.core.text.Convert;
import com.shengyu.common.exception.CustomException;
import com.shengyu.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

@Service
public class CustodyBookServiceImpl extends ServiceImpl<BusinessCustodyBookMapper, CustodyBook>
        implements ICustodyBookService {

    @Autowired
    private ICustodyItemService custodyItemService;

    @Autowired
    private ICustodyServeService custodyServeService;

    // ====================================================================================
    // =================================== manage =========================================
    // ====================================================================================

    @Override
    public IPage<CustodyBookDto> selectPage(Page<CustodyBookDto> page, CustodyBook custodyBook) {
        IPage<CustodyBookDto> ipage = baseMapper.selectPage(page, custodyBook);
        enrichCustodyBookDtos(ipage.getRecords(), false);
        return ipage;
    }

    @Override
    public CustodyBookDto selectById(Long id) {
        CustodyBookDto dto = baseMapper.selectCustodyBookById(id);
        enrichCustodyBookDtos(dto == null ? null : Arrays.asList(dto), false);
        return dto;
    }

    @Override
    public CustodyBookDto selectByIdUserId(Long id, Long userId) {
        CustodyBookDto book = selectById(id);
        if (null == book || !userId.equals(book.getBookUserId())) {
            throw new CustomException("无权限操作");
        }
        return book;
    }

    // ====================================================================================
    // ===================================== app ==========================================
    // ====================================================================================

    @Override
    public IPage<CustodyBookDto> selectPageIgnoreTenant(Page<CustodyBookDto> page, CustodyBook book) {
        IPage<CustodyBookDto> ipage = baseMapper.selectPageIgnoreTenant(page, book);
        enrichCustodyBookDtos(ipage.getRecords(), true);
        return ipage;
    }

    @Override
    public CustodyBookDto selectByIdIgnoreTenant(Long id) {
        CustodyBookDto dto = baseMapper.selectCustodyBookByIdIgnoreTenant(id);
        enrichCustodyBookDtos(dto == null ? null : Arrays.asList(dto), true);
        return dto;
    }

    @Override
    public CustodyBookDto selectByIdUserIdIgnoreTenant(Long id, Long userId) {
        CustodyBookDto dto = baseMapper.selectCustodyBookByIdIgnoreTenant(id);
        if (dto != null && !dto.getBookUserId().equals(userId)) {
            return null;
        }
        enrichCustodyBookDtos(dto == null ? null : Arrays.asList(dto), true);
        return dto;
    }

    @Override
    @Transactional
    public boolean saveCustodyBookIgnoreTenant(CustodyBook custodyBook) {
        // 使用 ignoreTenant 方法获取 CustodyServe 信息，确保能获取到正确的 tenantId
        CustodyServeDto serve = custodyServeService.selectByIdIgnoreTenant(custodyBook.getCustodyId());
        if (serve == null || serve.getStatus() == 0 || serve.getDeleteFlag() == 1) {
            throw new CustomException("托管服务已下架或不存在");
        }

        // 设置 CustodyBook 的 tenantId 为 CustodyServe 的 tenantId
        if (serve.getTenantId() != null) {
            custodyBook.setTenantId(serve.getTenantId());
        }

        custodyBook.setBookNo("SB" + SnowflakeIdUtils.getAsString());
        // 从 custody_serve 获取单位和服务商ID并赋值到 book
        fillDataFromServe(custodyBook);
        if (!validateTotalPrice(custodyBook)) {
            return false;
        }
        return baseMapper.insertWithServeTenantId(custodyBook) > 0;
    }

    @Override
    @Transactional
    public boolean updateByIdIgnoreTenant(CustodyBook custodyBook, Long userId) {
        CustodyBook existingBook = selectByIdUserId(custodyBook.getId(), userId);
        if (!"0".equals(existingBook.getBookStatus())) {
            throw new CustomException("当前状态无法修改");
        }

        // 如果更新了 custodyId，需要重新设置 tenantId
        if (custodyBook.getCustodyId() != null &&
                !custodyBook.getCustodyId().equals(existingBook.getCustodyId())) {
            // 使用 ignoreTenant 方法获取 CustodyServe 信息，确保能获取到正确的 tenantId
            CustodyServeDto serve = custodyServeService.selectByIdIgnoreTenant(custodyBook.getCustodyId());
            if (serve != null && serve.getTenantId() != null) {
                custodyBook.setTenantId(serve.getTenantId());
            }
        }

        // 从 custody_serve 获取单位和服务商ID并赋值到 book
        fillDataFromServe(custodyBook);
        if (!validateTotalPrice(custodyBook)) {
            return false;
        }
        return baseMapper.updateByIdWithServeTenantId(custodyBook) > 0;
    }

    @Override
    @Transactional
    public boolean removeByIdIgnoreTenant(Long id, Long userId) {
        CustodyBookDto book = selectByIdUserIdIgnoreTenant(id, userId);
        if (book == null) {
            throw new CustomException("记录不存在或无权操作");
        }
        return baseMapper.deleteByIdIgnoreTenant(id) > 0;
    }

    // ====================================================================================
    // =================================== private ========================================
    // ====================================================================================

    /**
     * 丰富CustodyBookDto列表，填充关联的作业项
     * @param records DTO列表
     * @param ignoreTenant 是否忽略租户
     */
    private void enrichCustodyBookDtos(List<CustodyBookDto> records, boolean ignoreTenant) {
        if (records == null || records.isEmpty()) {
            return;
        }
        for (CustodyBookDto record : records) {
            if (!StringUtils.isEmpty(record.getItemId())) {
                record.setItems(custodyItemService.loadItemsByIdString(record.getItemId(), ignoreTenant));
            }
        }
    }

    /**
     * 校验总价是否正确：根据 item_id 汇总 (unitPrice - subsidy) 并与传入 totalPrice 比较
     */
    private boolean validateTotalPrice(CustodyBook custodyBook) {
        BigDecimal calcTotal = BigDecimal.ZERO;
        String itemIdStr = custodyBook.getItemId();
        if (!StringUtils.isEmpty(itemIdStr)) {
            List<Long> itemIds = Arrays.asList(Convert.toLongArray(itemIdStr));
            if (!itemIds.isEmpty()) {
                List<CustodyItemDto> items = custodyItemService.selectListIgnoreTenant(itemIds);
                if (items != null) {
                    for (CustodyItemDto item : items) {
                        BigDecimal unitPrice = item.getUnitPrice() == null ? BigDecimal.ZERO : item.getUnitPrice();
                        BigDecimal subsidy = item.getSubsidy() == null ? BigDecimal.ZERO : item.getSubsidy();
                        calcTotal = calcTotal.add(unitPrice.subtract(subsidy));
                    }
                }
            }
        }
        custodyBook.setUnitPrice(calcTotal);
        if (custodyBook.getArea() != null && custodyBook.getArea().compareTo(BigDecimal.ZERO) > 0) {
            custodyBook.setTotalPrice(calcTotal.multiply(custodyBook.getArea()));
        }
        return true;
    }

    /**
     * 根据 custodyId 从 custody_serve 取 unit 和 serviceProviderId 并赋给 book
     */
    private void fillDataFromServe(CustodyBook custodyBook) {
        Long custodyId = custodyBook.getCustodyId();
        if (custodyId == null) {
            return;
        }
        CustodyServeDto serve = custodyServeService.selectByIdIgnoreTenant(custodyId);
        if (serve != null) {
            // 设置单位
            if (serve.getUnit() != null) {
                custodyBook.setUnit(serve.getUnit());
            }
            // 设置服务商ID
            if (serve.getServiceProviderId() != null) {
                custodyBook.setServiceProviderId(serve.getServiceProviderId());
            }
        }
    }
}
