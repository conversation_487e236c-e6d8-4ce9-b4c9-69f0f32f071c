package com.shengyu.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.shengyu.business.domain.CustodyServe;
import com.shengyu.business.domain.dto.CustodyServeDto;

import java.util.List;

/**
 * 托管服务服务层接口
 * 提供对托管服务理业务逻辑的操作定义
 */
public interface ICustodyServeService extends IService<CustodyServe> {

    // ====================================================================================
    // =================================== manage =========================================
    // ====================================================================================

    /**
     * 分页查询托管服务列表（返回DTO）
     */
    IPage<CustodyServeDto> selectPage(Page<CustodyServeDto> page, CustodyServeDto custodyServeDto);

    /**
     * 获取指定ID的托管服务对象
     *
     * @param id 托管ID
     * @return 符合指定ID的托管对象
     */
    CustodyServeDto selectById(Long id);

    /**
     * 新增托管服务
     *
     * @param custodyServe 托管服务
     * @return 新增结果
     */
    boolean saveCustodyServe(CustodyServe custodyServe);

    /**
     * 修改托管服务
     *
     * @param custodyServe 托管服务对象
     * @return 修改结果
     */
    boolean updateCustodyServe(CustodyServe custodyServe);

    /**
     * 根据ID列表删除托管服务对象
     *
     * @param ids 托管服务ID列表
     * @return 删除成功返回true，否则返回false
     */
    boolean removeByIds(List<Long> ids);

    // ====================================================================================
    // ===================================== app ==========================================
    // ====================================================================================

    /**
     * 分页查询托管服务列表 - 忽略租户
     */
    IPage<CustodyServeDto> selectPageIgnoreTenant(Page<CustodyServeDto> page, CustodyServeDto custodyServeDto);

    /**
     * 获取指定ID的托管服务对象 - 忽略租户
     *
     * @param id 托管ID
     * @return 符合指定ID的托管对象
     */
    CustodyServeDto selectByIdIgnoreTenant(Long id);

}