package com.shengyu.business.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dxy.library.snowflake.SnowflakeIdUtils;
import com.shengyu.business.domain.PlaneBook;
import com.shengyu.business.domain.PlaneServe;
import com.shengyu.business.domain.dto.PlaneBookDto;
import com.shengyu.business.mapper.BusinessPlaneBookMapper;
import com.shengyu.business.service.IPlaneBookService;
import com.shengyu.business.service.IPlaneServeService;
import com.shengyu.common.exception.CustomException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

/**
 * 飞防服务预定表 Service实现
 *
 * <AUTHOR>
 * @since 2025-07-12 17:34:46
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PlaneBookServiceImpl extends ServiceImpl<BusinessPlaneBookMapper, PlaneBook> 
        implements IPlaneBookService {

    private final IPlaneServeService planeServeService;

    // ====================================================================================
    // =================================== manage =========================================
    // ====================================================================================

    @Override
    public IPage<PlaneBookDto> selectPage(Page<PlaneBookDto> page, PlaneBook book) {
        if (Objects.isNull(page)) {
            throw new CustomException("分页参数不能为空");
        }
        return baseMapper.selectPage(page, book);
    }

    @Override
    public PlaneBook selectById(Long id) {
        PlaneBook planeBook = baseMapper.selectById(id);
        if (Objects.isNull(planeBook)) {
            throw new CustomException("飞防服务预定不存在");
        }
        return planeBook;
    }

    // ====================================================================================
    // ===================================== app ==========================================
    // ====================================================================================

    @Override
    public IPage<PlaneBookDto> selectPageIgnoreTenant(Page<PlaneBookDto> page, PlaneBook book) {
        if (Objects.isNull(page)) {
            throw new CustomException("分页参数不能为空");
        }
        return baseMapper.selectPageIgnoreTenant(page, book);
    }

    @Override
    public PlaneBookDto selectByIdUserIdIgnoreTenant(Long id, Long userId) {
        PlaneBookDto planeBook = baseMapper.selectByIdIgnoreTenant(id);
        if (Objects.isNull(planeBook)) {
            throw new CustomException("飞防服务预定不存在");
        }
        if (!Objects.equals(planeBook.getBookUserId(), userId)) {
            throw new CustomException("无权操作");
        }
        return planeBook;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean savePlaneBookIgnoreTenant(PlaneBook planeBook) {
        if (Objects.isNull(planeBook)) {
            throw new CustomException("参数不能为空");
        }
        if (Objects.isNull(planeBook.getServeId())) {
            throw new CustomException("飞防服务ID不能为空");
        }

        PlaneServe planeServe = planeServeService.selectByIdIgnoreTenant(planeBook.getServeId());
        if (Objects.isNull(planeServe)) {
            throw new CustomException("飞防服务不存在");
        }
        planeBook.setBookNo("PB" + SnowflakeIdUtils.getAsString());
        planeBook.setTenantId(planeServe.getTenantId());
        planeBook.setBookStatus("0");
        planeBook.setUnitPrice(planeServe.getUnitPrice());
        planeBook.setTotalPrice(planeBook.getUnitPrice().multiply(planeBook.getArea()));

        return baseMapper.insertIgnoreTenant(planeBook) > 0;
    }

    @Override
    public boolean updateByIdIgnoreTenant(PlaneBook planeBook, Long userId) {
        if (Objects.isNull(planeBook) || Objects.isNull(planeBook.getId())) {
            throw new CustomException("参数错误");
        }
        // 权限验证
        selectByIdUserIdIgnoreTenant(planeBook.getId(), userId);
        return baseMapper.updateByIdIgnoreTenant(planeBook) > 0;
    }

    @Override
    public boolean removeByIdIgnoreTenant(Long id, Long userId) {
        if (Objects.isNull(id)) {
            throw new CustomException("参数错误");
        }
        // 权限验证
        selectByIdUserIdIgnoreTenant(id, userId);
        return baseMapper.deleteByIdIgnoreTenant(id) > 0;
    }
}
