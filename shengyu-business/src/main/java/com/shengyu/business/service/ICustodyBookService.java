package com.shengyu.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.shengyu.business.domain.CustodyBook;
import com.shengyu.business.domain.dto.CustodyBookDto;

/**
 * <AUTHOR>
 * @description 针对表【t_custody_book(托管预定表)】的数据库操作Service
 * @createDate 2025-07-12 15:22:06
 */
public interface ICustodyBookService extends IService<CustodyBook> {

    // ====================================================================================
    // =================================== manage =========================================
    // ====================================================================================

    /**
     * 分页查询托管预定列表（返回DTO）
     */
    IPage<CustodyBookDto> selectPage(Page<CustodyBookDto> page, CustodyBook book);

    /**
     * 获取指定ID的全程托管对象
     *
     * @param id 远程托管ID
     * @return 符合指定ID的远程托管对象
     */
    CustodyBookDto selectById(Long id);

    /**
     * 根据ID和用户ID查询托管预定
     *
     * @param id     全程托管预定ID
     * @param userId 用户ID
     * @return 符合条件的全程托管预定对象
     */
    CustodyBookDto selectByIdUserId(Long id, Long userId);

    // ====================================================================================
    // ===================================== app ==========================================
    // ====================================================================================

    /**
     * 分页查询托管预定列表（返回DTO） - 忽略租户信息
     */
    IPage<CustodyBookDto> selectPageIgnoreTenant(Page<CustodyBookDto> page, CustodyBook book);

    /**
     * 获取指定ID的全程托管对象 - 忽略租户信息
     *
     * @param id 远程托管ID
     * @return 符合指定ID的远程托管对象
     */
    CustodyBookDto selectByIdIgnoreTenant(Long id);

    /**
     * 根据ID和用户ID查询托管预定 - 忽略租户信息
     *
     * @param id     全程托管预定ID
     * @param userId 用户ID
     * @return 符合条件的全程托管预定对象
     */
    CustodyBookDto selectByIdUserIdIgnoreTenant(Long id, Long userId);

    /**
     * 保存全程托管预定 - 忽略租户
     *
     * @param custodyBook 待保存的全程托管预定对象
     * @return 是否保存成功
     */
    boolean saveCustodyBookIgnoreTenant(CustodyBook custodyBook);

    /**
     * 根据ID和用户校验后更新全程托管预定 - 忽略租户
     *
     * @param custodyBook 待更新的全程托管预定对象
     * @param userId      用户ID
     * @return 是否更新成功
     */
    boolean updateByIdIgnoreTenant(CustodyBook custodyBook, Long userId);

    /**
     * 根据ID和用户校验后删除全程托管预定 - 忽略租户
     *
     * @param id     全程托管预定ID
     * @param userId 用户ID
     * @return 是否删除成功
     */
    boolean removeByIdIgnoreTenant(Long id, Long userId);
}
