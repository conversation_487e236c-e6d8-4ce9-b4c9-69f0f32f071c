package com.shengyu.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.shengyu.business.domain.MachineServe;
import com.shengyu.business.domain.dto.MachineServeDto;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【t_business_machine_serve(农机服务表)】的数据库操作Service
 * @createDate 2025-07-12 17:34:46
 */
public interface IMachineServeService extends IService<MachineServe> {

    /**
     * 分页查询农机服务列表（返回DTO）
     */
    IPage<MachineServeDto> selectPage(Page<MachineServeDto> page, MachineServe machineServe);

    // ====================================================================================
    // =================================== manage =========================================
    // ====================================================================================

    /**
     * 获取指定ID的农机服务对象
     *
     * @param id 农机服务ID
     * @return 符合指定ID的农机服务对象
     */
    MachineServe selectById(Long id);

    /**
     * 保存农机服务
     *
     * @param machineServe 待保存的农机服务对象
     * @return 是否保存成功
     */
    boolean saveMachineServe(MachineServe machineServe);

    /**
     * 根据ID和用户校验后更新农机服务
     *
     * @param machineServe 待更新的农机服务对象
     * @return 是否更新成功
     */
    boolean updateMachineServe(MachineServe machineServe);

    /**
     * 根据ID批量删除农机服务
     *
     * @param ids 主键ID列表
     * @return 是否删除成功
     */
    boolean removeByIds(List<Long> ids);

    // ====================================================================================
    // ===================================== app ==========================================
    // ====================================================================================

    /**
     * 根据服务类型查询单个农机服务（上架且未删除）- 忽略租户
     *
     * @param serveType 服务类型编码
     * @return 农机服务对象
     */
    MachineServe selectByServeTypeIgnoreTenant(String serveType);

}
