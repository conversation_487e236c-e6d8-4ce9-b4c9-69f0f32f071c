package com.shengyu.business.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.shengyu.business.domain.CustodyItem;
import com.shengyu.business.domain.dto.CustodyItemDto;
import com.shengyu.business.mapper.BusinessCustodyItemMapper;
import com.shengyu.business.mapper.BusinessCustodyServeMapper;
import com.shengyu.business.service.ICustodyItemService;
import com.shengyu.common.exception.CustomException;
import com.shengyu.common.core.text.Convert;
import com.shengyu.common.utils.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 托管作业项服务层实现
 */
@Service
public class CustodyItemServiceImpl extends ServiceImpl<BusinessCustodyItemMapper, CustodyItem> implements ICustodyItemService {

    @Resource
    private BusinessCustodyServeMapper businessCustodyServeMapper;

    // ====================================================================================
    // =================================== manage =========================================
    // ====================================================================================

    @Override
    public IPage<CustodyItemDto> selectPage(Page<CustodyItemDto> page, CustodyItem custodyItem) {
        return baseMapper.selectPage(page, custodyItem);
    }

    @Override
    public CustodyItemDto selectById(Long id) {
        CustodyItemDto dto = baseMapper.selectById(id);
        if (null == dto) {
            throw new CustomException("该操作项已不存在!");
        }
        return dto;
    }

    @Override
    @Transactional
    public boolean saveCustodyItem(CustodyItem custodyItem) {
        return baseMapper.insert(custodyItem) > 0;
    }

    /**
     * 更新托管作业项
     *
     * @param custodyItem 托管作业项
     * @return 是否成功
     */
    @Override
    @Transactional
    public boolean updateCustodyItem(CustodyItem custodyItem) {
        selectById(custodyItem.getId());
        return baseMapper.updateById(custodyItem) > 0;
    }

    @Override
    @Transactional
    public boolean removeByIds(List<Long> ids) {
        for (Long id : ids) {
            selectById(id);
            if (businessCustodyServeMapper.itemIdIsInServe(id) > 0) {
                throw new CustomException("该作业项已被托管服务使用，请先下架托管服务解除关联再删除!");
            }
        }
        return ids.size() == baseMapper.deleteBatchIds(ids);
    }

    @Override
    public List<CustodyItemDto> selectList(List<Long> ids) {
        ids = ids.stream()
                .sorted()
                .collect(Collectors.toList());
        return baseMapper.selectList(ids);
    }

    // ====================================================================================
    // ===================================== app ==========================================
    // ====================================================================================

    @Override
    public List<CustodyItemDto> selectListIgnoreTenant(List<Long> ids) {
        ids = ids.stream()
                .sorted()
                .collect(Collectors.toList());
        return baseMapper.selectListIgnoreTenant(ids);
    }

    @Override
    public CustodyItemDto selectByIdIgnoreTenant(Long id) {
        CustodyItemDto dto = baseMapper.selectByIdIgnoreTenant(id);
        if (null == dto) {
            throw new CustomException("该操作项已不存在!");
        }
        return dto;
    }

    // ====================================================================================
    // =================================== shared util ====================================
    // ====================================================================================

    public List<CustodyItemDto> loadItemsByIdString(String itemIdStr, boolean ignoreTenant) {
        if (StringUtils.isEmpty(itemIdStr)) return Collections.emptyList();
        List<Long> ids = Arrays.asList(Convert.toLongArray(itemIdStr));
        return ids.isEmpty() ? Collections.emptyList() : (ignoreTenant ? selectListIgnoreTenant(ids) : selectList(ids));
    }
}