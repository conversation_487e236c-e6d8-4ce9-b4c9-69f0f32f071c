package com.shengyu.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.shengyu.business.domain.SupplyDemand;
import com.shengyu.business.domain.dto.SupplyDemandDto;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【t_business_supply_demand(供需信息表)】的数据库操作Service
 * @createDate 2025-07-16 14:32:12
 */
public interface ISupplyDemandService extends IService<SupplyDemand> {

    // ====================================================================================
    // =================================== manage =========================================
    // ====================================================================================

    /**
     * 分页查询供需信息列表（租户隔离）
     *
     * @param page 分页参数
     * @param supplyDemand 查询条件
     * @return 分页结果
     */
    IPage<SupplyDemandDto> selectPage(Page<SupplyDemandDto> page, SupplyDemand supplyDemand);

    /**
     * 查询供需信息列表（租户隔离）
     *
     * @param supplyDemand 查询条件
     * @return 供需信息列表
     */
    List<SupplyDemandDto> selectList(SupplyDemand supplyDemand);

    /**
     * 根据ID查询供需信息（租户隔离）
     *
     * @param id 供需信息ID
     * @return 供需信息详情
     */
    SupplyDemandDto selectOne(Long id);

    // ====================================================================================
    // ===================================== app ==========================================
    // ====================================================================================

    /**
     * 分页查询供需信息列表（忽略租户）
     *
     * @param page 分页参数
     * @param supplyDemand 查询条件
     * @return 分页结果
     */
    IPage<SupplyDemandDto> selectPageIgnoreTenant(Page<SupplyDemandDto> page, SupplyDemand supplyDemand);

    /**
     * 根据ID查询有效的供需信息（忽略租户）
     *
     * @param id 供需信息ID
     * @return 供需信息详情
     * @throws com.shengyu.common.exception.CustomException 当记录不存在或已删除时抛出
     */
    SupplyDemandDto selectValidByIdIgnoreTenant(Long id);

    /**
     * 根据ID和用户校验后查询供需信息（忽略租户）
     *
     * @param id 供需信息ID
     * @param userId 用户ID
     * @return 供需信息详情
     * @throws com.shengyu.common.exception.CustomException 当记录不存在或无权限时抛出
     */
    SupplyDemandDto selectByIdUserIdIgnoreTenant(Long id, Long userId);

    /**
     * 保存供需信息（忽略租户）
     *
     * @param supplyDemand 待保存的供需信息对象
     * @return 是否保存成功
     */
    boolean saveSupplyDemandIgnoreTenant(SupplyDemand supplyDemand);

    /**
     * 根据ID和用户校验后更新供需信息（忽略租户）
     *
     * @param supplyDemand 供需信息对象
     * @param userId       用户ID
     * @return 是否更新成功
     * @throws com.shengyu.common.exception.CustomException 当记录不存在或无权限时抛出
     */
    boolean updateByIdUserIdIgnoreTenant(SupplyDemand supplyDemand, Long userId);

    /**
     * 根据ID和用户校验后删除供需信息（忽略租户）
     *
     * @param id     供需信息ID
     * @param userId 用户ID
     * @return 是否删除成功
     * @throws com.shengyu.common.exception.CustomException 当记录不存在或无权限时抛出
     */
    boolean removeByIdUserIdIgnoreTenant(Long id, Long userId);
}
