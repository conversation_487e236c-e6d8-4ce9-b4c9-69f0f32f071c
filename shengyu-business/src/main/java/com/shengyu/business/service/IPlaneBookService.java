package com.shengyu.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.shengyu.business.domain.PlaneBook;
import com.shengyu.business.domain.dto.PlaneBookDto;

/**
 * 飞防服务预定表 Service接口
 *
 * <AUTHOR>
 * @since 2025-07-12 17:34:46
 */
public interface IPlaneBookService extends IService<PlaneBook> {

    // ====================================================================================
    // =================================== manage =========================================
    // ====================================================================================

    /**
     * 分页查询飞防服务预定列表（租户隔离）
     *
     * @param page 分页参数
     * @param book 查询条件
     * @return 分页结果
     */
    IPage<PlaneBookDto> selectPage(Page<PlaneBookDto> page, PlaneBook book);

    /**
     * 根据ID查询飞防服务预定（租户隔离）
     *
     * @param id 飞防服务预定ID
     * @return 飞防服务预定信息
     */
    PlaneBook selectById(Long id);


    // ====================================================================================
    // ===================================== app ==========================================
    // ====================================================================================

    /**
     * 分页查询飞防服务预定列表（忽略租户）
     *
     * @param page 分页参数
     * @param book 查询条件
     * @return 分页结果
     */
    IPage<PlaneBookDto> selectPageIgnoreTenant(Page<PlaneBookDto> page, PlaneBook book);

    /**
     * 根据ID和用户ID查询飞防服务预定（忽略租户）
     *
     * @param id     飞防服务预定ID
     * @param userId 用户ID
     * @return 飞防服务预定信息
     */
    PlaneBook selectByIdUserIdIgnoreTenant(Long id, Long userId);

    /**
     * 新增飞防服务预定（忽略租户）
     * <p>
     * 1. 根据 serveId 跨租户获取飞防服务
     * 2. 将服务的 tenantId 写入预定
     * 3. 关联当前用户ID
     *
     * @param planeBook 飞防服务预定信息
     * @return 是否成功
     */
    boolean savePlaneBookIgnoreTenant(PlaneBook planeBook);

    /**
     * 更新飞防服务预定（忽略租户 + 用户校验）
     *
     * @param planeBook 飞防服务预定信息
     * @param userId    用户ID
     * @return 是否成功
     */
    boolean updateByIdIgnoreTenant(PlaneBook planeBook, Long userId);

    /**
     * 删除飞防服务预定（忽略租户 + 用户校验）
     *
     * @param id     飞防服务预定ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean removeByIdIgnoreTenant(Long id, Long userId);
}
