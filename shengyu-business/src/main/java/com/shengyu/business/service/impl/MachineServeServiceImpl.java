package com.shengyu.business.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dxy.library.snowflake.SnowflakeIdUtils;
import com.shengyu.business.domain.MachineServe;
import com.shengyu.business.mapper.BusinessMachineServeMapper;
import com.shengyu.business.service.IMachineServeService;
import com.shengyu.common.exception.CustomException;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.shengyu.business.domain.dto.MachineServeDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 针对表【t_business_machine_serve(农机服务表)】的数据库操作Service实现
 * @createDate 2025-07-12 17:34:46
 */
@Service
public class MachineServeServiceImpl extends ServiceImpl<BusinessMachineServeMapper, MachineServe>
        implements IMachineServeService {

    // ====================================================================================
    // =================================== manage =========================================
    // ====================================================================================

    @Override
    public MachineServe selectById(Long id) {
        return baseMapper.selectById(id);
    }

    @Override
    public IPage<MachineServeDto> selectPage(Page<MachineServeDto> page, MachineServe machineServe) {
        return baseMapper.selectPage(page, machineServe);
    }

    @Override
    @Transactional
    public boolean saveMachineServe(MachineServe machineServe) {
        machineServe.setServeNo("MS" + SnowflakeIdUtils.getAsLong());
        return super.save(machineServe);
    }

    @Override
    @Transactional
    public boolean updateMachineServe(MachineServe machineServe) {
        MachineServe oldMachineServe = selectById(machineServe.getId());
        if (null == oldMachineServe) {
            throw new CustomException("农机服务不存在");
        }
        // 规则：整型比较使用 Objects.equals，避免 NPE
        if (Objects.equals(oldMachineServe.getStatus(), 1)) {
            throw new CustomException("该操作项已上架，请先下架再编辑!");
        }
        return baseMapper.updateById(machineServe) > 0;
    }

    @Override
    @Transactional
    public boolean removeByIds(List<Long> ids) {
        for (Long id : ids) {
            MachineServe machineServe = this.getById(id);
            if (null == machineServe) {
                throw new CustomException("该操作项已不存在!");
            }
            if (Objects.equals(machineServe.getStatus(), 1)) {
                throw new CustomException("该操作项已上架，请先下架再删除!");
            }
        }
        baseMapper.deleteBatchIds(ids);
        return true;
    }

    // ====================================================================================
    // ===================================== app ==========================================
    // ====================================================================================

    @Override
    public MachineServe selectByServeTypeIgnoreTenant(String serveType) {
        return baseMapper.selectByServeTypeIgnoreTenant(serveType);
    }

}




