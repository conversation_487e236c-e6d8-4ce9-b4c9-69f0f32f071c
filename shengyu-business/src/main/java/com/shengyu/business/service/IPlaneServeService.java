package com.shengyu.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.shengyu.business.domain.PlaneServe;
import com.shengyu.business.domain.dto.PlaneServeDto;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【t_business_plane_serve(飞防服务表)】的数据库操作Service
 * @createDate 2025-07-12 17:34:46
 */
public interface IPlaneServeService extends IService<PlaneServe> {

    // ====================================================================================
    // =================================== manage =========================================
    // ====================================================================================

    /**
     * 分页查询飞防服务列表（租户隔离）
     *
     * @param page       分页参数
     * @param planeServe 查询条件
     * @return 分页结果
     */
    IPage<PlaneServeDto> selectPage(Page<PlaneServeDto> page, PlaneServe planeServe);

    /**
     * 根据ID查询飞防服务（租户隔离）
     *
     * @param id 飞防服务ID
     * @return 飞防服务信息
     */
    PlaneServe selectById(Long id);

    /**
     * 新增飞防服务（租户隔离）
     *
     * @param planeServe 飞防服务信息
     * @return 是否成功
     */
    boolean savePlaneServe(PlaneServe planeServe);

    /**
     * 更新飞防服务（租户隔离）
     *
     * @param planeServe 飞防服务信息
     * @return 是否成功
     */
    boolean updatePlaneServe(PlaneServe planeServe);

    /**
     * 批量删除飞防服务（租户隔离）
     *
     * @param ids 待删除的ID列表
     * @return 是否成功
     */
    boolean removeByIds(List<Long> ids);

    // ====================================================================================
    // ===================================== app ==========================================
    // ====================================================================================

    /**
     * 根据服务类型查询（忽略租户，仅上架）
     *
     * @param serveType 服务类型编码
     * @return _blank
     */
    PlaneServe selectByServeTypeIgnoreTenant(String serveType);

    /**
     * 根据ID查询（忽略租户）
     *
     * @param id 飞防服务ID
     * @return 飞防服务信息
     */
    PlaneServe selectByIdIgnoreTenant(Long id);
}