package com.shengyu.business.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dxy.library.snowflake.SnowflakeIdUtils;
import com.shengyu.business.domain.ServiceProvider;
import com.shengyu.business.domain.dto.ServiceProviderDto;
import com.shengyu.business.mapper.ServiceProviderMapper;
import com.shengyu.business.service.IServiceProviderService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 商城服务商信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Service
public class ServiceProviderServiceImpl extends ServiceImpl<ServiceProviderMapper, ServiceProvider> implements IServiceProviderService {

    @Override
    public ServiceProviderDto selectOne(Long id) {
        return baseMapper.selectServiceProviderById(id);
    }

    @Override
    public IPage<ServiceProviderDto> selectPage(Page<ServiceProviderDto> page, ServiceProvider serviceProvider) {
        return baseMapper.selectPage(page, serviceProvider);
    }

    @Override
    public List<ServiceProviderDto> selectList(ServiceProvider serviceProvider) {
        return baseMapper.selectList(serviceProvider);
    }

    @Override
    @Transactional
    public boolean saveServiceProvider(ServiceProvider entity) {
        entity.setProviderNo("SP" + SnowflakeIdUtils.getAsLong());
        return super.save(entity);
    }

    @Override
    @Transactional
    public boolean updateServiceProvider(ServiceProvider entity) {
        return super.updateById(entity);
    }

    @Override
    @Transactional
    public boolean removeByIds(List<Long> ids) {
        // todo
        // 在删除之前，检查每个服务商关联的用户，对应的服务

        baseMapper.deleteBatchIds(ids);
        return true;
    }
}
