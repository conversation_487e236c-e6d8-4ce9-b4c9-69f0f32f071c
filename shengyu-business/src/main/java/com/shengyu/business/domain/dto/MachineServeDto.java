package com.shengyu.business.domain.dto;

import com.shengyu.business.domain.MachineServe;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 农机服务DTO，包含服务类型名称
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "农机服务DTO")
public class MachineServeDto extends MachineServe {
    @ApiModelProperty("服务类型名称")
    private String serveTypeName;

    @ApiModelProperty("服务状态名称")
    private String statusName;

    @ApiModelProperty("部门名称")
    private String deptName;

    public void setDepartCode(Long departCode) {
        this.setDeptId(departCode);
    }
}
