package com.shengyu.business.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.shengyu.business.domain.CustodyBook;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 托管预定dto
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "托管预定dto")
public class CustodyBookDto extends CustodyBook {

    @ApiModelProperty("托管名称")
    private String custodyName;

    @ApiModelProperty("预约状态名称")
    private String bookStatusName;

    @ApiModelProperty("托管类型名称")
    private String custodyCategoryName;

    @ApiModelProperty("作业项列表")
    private List<CustodyItemDto> items;

    @ApiModelProperty("地址区域名称")
    private String addressRegionName;

    @ApiModelProperty("作物类型名称")
    private String cropTypeName;

    @ApiModelProperty("服务商")
    private String serviceProvider;
}