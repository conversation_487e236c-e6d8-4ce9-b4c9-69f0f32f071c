package com.shengyu.business.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.shengyu.common.core.domain.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 飞防服务预定表
 *
 * @TableName t_business_plane_book
 */
@TableName(value = "t_business_plane_book")
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "飞防服务预定表")
public class PlaneBook extends TenantEntity {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("预定编号")
    @TableField(value = "book_no")
    private String bookNo;

    @ApiModelProperty("用户ID")
    @TableField(value = "book_user_id")
    private Long bookUserId;

    @ApiModelProperty("服务类型")
    @TableField(value = "serve_type")
    private String serveType;

    @ApiModelProperty("飞防服务ID")
    @TableField(value = "serve_id")
    private Long serveId;

    @ApiModelProperty("预定状态（booking预约中，processing处理中，completed已完成）")
    @TableField(value = "book_status")
    private String bookStatus;

    @ApiModelProperty("联系人姓名")
    @TableField(value = "contact_name")
    private String contactName;

    @ApiModelProperty("联系电话")
    @TableField(value = "phone")
    private String phone;

    @ApiModelProperty("身份证号码")
    @TableField(value = "id_card")
    private String idCard;

    @ApiModelProperty("地块数量")
    @TableField(value = "plot_count")
    private BigDecimal plotCount;

    @ApiModelProperty("服务面积(亩)")
    @TableField(value = "area")
    private BigDecimal area;

    @ApiModelProperty("部门ID")
    @TableField(value = "dept_id")
    private Long deptId;

    @ApiModelProperty("详细地址")
    @TableField(value = "address_detail")
    private String addressDetail;

    @ApiModelProperty("单价")
    @TableField(value = "unit_price")
    private BigDecimal unitPrice;

    @ApiModelProperty("服务总价")
    @TableField(value = "total_price")
    private BigDecimal totalPrice;

    @ApiModelProperty("有效期限")
    @TableField(value = "expiration_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expirationDate;

    @ApiModelProperty("软删除标识:0-未删除,1-已删除")
    @TableField(value = "delete_flag")
    @TableLogic
    private Integer deleteFlag;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}