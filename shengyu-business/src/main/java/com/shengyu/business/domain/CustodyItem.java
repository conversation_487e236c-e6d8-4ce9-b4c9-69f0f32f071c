package com.shengyu.business.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.shengyu.common.core.domain.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 作业项实体类
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "作业项表")
@TableName(value = "t_business_custody_item")
public class CustodyItem extends TenantEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("作业项名称")
    private String name;

    @ApiModelProperty("作业项内容")
    private String content;
    
    @ApiModelProperty("作业项单价")
    private BigDecimal unitPrice;

    @ApiModelProperty("作业项单位（如亩、次）")
    private String unit;

    @ApiModelProperty("补贴金额")
    private BigDecimal subsidy;

    @ApiModelProperty("折扣率")
    private BigDecimal discount;

    @ApiModelProperty("作物类型")
    private String cropType;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("软删除标识: 0-未删除, 1-已删除")
    @TableLogic(value = "0", delval = "1")
    private Integer deleteFlag;

}