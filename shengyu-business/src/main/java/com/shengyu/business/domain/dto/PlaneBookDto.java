package com.shengyu.business.domain.dto;

import com.shengyu.business.domain.PlaneBook;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 业务平面预约数据传输对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("飞防预定DTO")
public class PlaneBookDto extends PlaneBook {

    @ApiModelProperty("服务类型名称")
    private String serveTypeName;

    @ApiModelProperty("预约状态名称")
    private String bookStatusName;

    @ApiModelProperty("部门名称")
    private String deptName;
}
