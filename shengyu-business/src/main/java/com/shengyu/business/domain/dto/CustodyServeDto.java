package com.shengyu.business.domain.dto;

import com.shengyu.business.domain.CustodyServe;
import com.shengyu.common.utils.StringUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "托管服务dto")
public class CustodyServeDto extends CustodyServe {
    @ApiModelProperty("作业类型名称")
    private String cropTypeName;

    @ApiModelProperty("托管状态名称")
    private String statusName;

    @ApiModelProperty("托管类型名称")
    private String custodyCategoryName;

    @ApiModelProperty("省名称")
    private String provinceName;

    @ApiModelProperty("市名称")
    private String cityName;

    @ApiModelProperty("区县名称")
    private String countyName;

    @ApiModelProperty("作业项列表")
    private List<CustodyItemDto> items;

    @ApiModelProperty("部门名称")
    private String deptName;

    /**
     * 设置部门编码，并转换为部门ID
     * 用于兼容移动端应用传递的部门编码字符串，将其转换为Long类型并设置到deptId字段
     *
     * @param departCode 部门编码字符串
     */
    public void setDepartCode(String departCode) {
        if(StringUtils.isNotEmpty(departCode)) {
            this.setDeptId(Long.valueOf(departCode));
        }
    }
}