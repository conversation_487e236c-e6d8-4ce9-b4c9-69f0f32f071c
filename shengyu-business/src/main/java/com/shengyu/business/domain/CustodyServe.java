package com.shengyu.business.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.shengyu.business.enums.CustodyCategoryEnum;
import com.shengyu.common.core.domain.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 托管服务实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "托管服务表")
@TableName(value = "t_business_custody_serve")
public class CustodyServe extends TenantEntity {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 托管名称
     */
    @ApiModelProperty("托管名称")
    private String name;

    /**
     * 托管类型
     */
    @ApiModelProperty("托管类型")
    private CustodyCategoryEnum custodyCategory;

    /**
     * 托管服务内容（如整地、播种、施肥等）
     */
    @ApiModelProperty("托管服务内容（如整地、播种、施肥等）")
    private String serviceItems;

    /**
     * d
     * 托管单价
     */
    @ApiModelProperty("托管单价")
    private BigDecimal unitPrice;

    /**
     * 托管单位（如亩、次）
     */
    @ApiModelProperty("托管单位（如亩、次）")
    private String unit;

    /**
     * 补贴金额
     */
    @ApiModelProperty("补贴金额")
    private BigDecimal subsidy;

    /**
     * 折扣率
     */
    @ApiModelProperty("折扣率")
    private BigDecimal discount;

    /**
     * 托管介绍
     */
    @ApiModelProperty("托管介绍")
    private String description;

    /**
     * 标签（逗号分隔）
     */
    @ApiModelProperty("标签（逗号分隔）")
    private String tags;

    /**
     * 作物类型
     */
    @ApiModelProperty("作物类型")
    private String cropType;

    /**
     * 图片URL
     */
    @ApiModelProperty("图片URL")
    private String imageUrl;

    /**
     * 托管状态: 0-下架, 1-上架，默认值为1
     */
    @ApiModelProperty("托管状态: 0-下架, 1-上架，默认值为1")
    private Integer status;

    /**
     * 服务商
     */
    @ApiModelProperty("服务商")
    private String serviceProvider;

    /**
     * 服务商ID
     */
    @ApiModelProperty("服务商ID")
    private Long serviceProviderId;

    /**
     * 部门ID
     */
    @ApiModelProperty("部门ID")
    @TableField(value = "dept_id")
    private Long deptId;

    /**
     * 软删除标识: 0-未删除, 1-已删除
     */
    @ApiModelProperty("软删除标识: 0-未删除, 1-已删除")
    @TableLogic(value = "0", delval = "1")
    private Integer deleteFlag;
}