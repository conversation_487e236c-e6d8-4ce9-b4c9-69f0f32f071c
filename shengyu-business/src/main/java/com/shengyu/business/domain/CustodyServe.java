package com.shengyu.business.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.shengyu.business.enums.CustodyCategoryEnum;
import com.shengyu.common.core.domain.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 托管服务实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "托管服务表")
@TableName(value = "t_business_custody_serve")
public class CustodyServe extends TenantEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("托管名称")
    private String name;

    @ApiModelProperty("托管类型")
    private CustodyCategoryEnum custodyCategory;

    @ApiModelProperty("托管服务内容（如整地、播种、施肥等）")
    private String serviceItems;

    @ApiModelProperty("托管单价")
    private BigDecimal unitPrice;

    @ApiModelProperty("托管单位（如亩、次）")
    private String unit;

    @ApiModelProperty("补贴金额")
    private BigDecimal subsidy;

    @ApiModelProperty("折扣率")
    private BigDecimal discount;

    @ApiModelProperty("托管介绍")
    private String description;

    @ApiModelProperty("标签（逗号分隔）")
    private String tags;

    @ApiModelProperty("作物类型")
    private String cropType;

    @ApiModelProperty("图片URL")
    private String imageUrl;

    @ApiModelProperty("托管状态: 0-下架, 1-上架，默认值为1")
    private Integer status;

    @ApiModelProperty("服务商")
    private String serviceProvider;

    @ApiModelProperty("服务商ID")
    private Long serviceProviderId;

    @ApiModelProperty("部门ID")
    private Long deptId;

    @ApiModelProperty("软删除标识: 0-未删除, 1-已删除")
    @TableLogic(value = "0", delval = "1")
    private Integer deleteFlag;
}