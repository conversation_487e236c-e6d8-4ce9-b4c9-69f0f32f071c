package com.shengyu.business.domain.dto;

import com.shengyu.business.domain.SupplyDemand;
import com.shengyu.common.utils.StringUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 托管预定dto
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "托管预定dto")
public class SupplyDemandDto extends SupplyDemand {

    @ApiModelProperty("供需类型名称")
    private String infoTypeName;

    @ApiModelProperty("服务类型名称")
    private String serveTypeName;

    @ApiModelProperty("作物类型名称")
    private String cropTypeName;

    @ApiModelProperty("审核状态名称")
    private String auditStatusName;

    @ApiModelProperty("部门名称")
    private String deptName;

    /**
     * 设置部门编码，并转换为部门ID
     * 用于兼容移动端应用传递的部门编码字符串，将其转换为Long类型并设置到deptId字段
     *
     * @param departCode 部门编码字符串
     */
    public void setDepartCode(String departCode) {
        if (StringUtils.isNotEmpty(departCode)) {
            this.setDeptId(Long.parseLong(departCode));
        }
    }
}