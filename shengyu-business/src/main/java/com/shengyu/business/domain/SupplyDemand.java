package com.shengyu.business.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.shengyu.business.enums.AuditStatusEnum;
import com.shengyu.common.core.domain.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 供需信息表
 *
 * @TableName t_business_supply_demand
 */
@TableName(value = "t_business_supply_demand")
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "供需信息表")
public class SupplyDemand extends TenantEntity {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @ApiModelProperty("用户ID")
    @TableField(value = "user_id")
    private Long userId;

    /**
     * 信息标题
     */
    @ApiModelProperty("信息标题")
    @TableField(value = "info_title")
    private String infoTitle;

    /**
     * 信息类型（如供求，需求等）
     */
    @ApiModelProperty("信息类型")
    @TableField(value = "info_type")
    private String infoType;

    /**
     * 供需类型，服务类型（农资，农机，种子，用工，农产品，农业服务，农村资产，其他）
     */
    @ApiModelProperty("供需类型，服务类型（农资，农机，种子，用工，农产品，农业服务，农村资产，其他）")
    @TableField(value = "serve_type")
    private String serveType;

    /**
     * 作物类型（如水稻、小麦等）
     */
    @ApiModelProperty("作物类型")
    @TableField(value = "crop_type")
    private String cropType;

    /**
     * 联系人姓名
     */
    @ApiModelProperty("联系人姓名")
    @TableField(value = "contact_name")
    private String contactName;

    /**
     * 联系电话
     */
    @ApiModelProperty("联系电话")
    @TableField(value = "phone_number")
    private String phoneNumber;

    /**
     * 身份证号码
     */
    @ApiModelProperty("身份证号码")
    @TableField(value = "id_card")
    private String idCard;



    /**
     * 图片地址
     */
    @ApiModelProperty("图片地址")
    @TableField(value = "image_url")
    private String imageUrl;

    /**
     * 部门ID
     */
    @ApiModelProperty("部门ID")
    @TableField(value = "dept_id")
    private Long deptId;

    /**
     * 详细地址
     */
    @ApiModelProperty("详细地址")
    @TableField(value = "address_detail")
    private String addressDetail;

    /**
     * 详细描述
     */
    @ApiModelProperty("详细描述")
    @TableField(value = "description")
    private String description;

    /**
     * 发布时间
     */
    @ApiModelProperty("发布时间")
    @TableField(value = "publish_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date publishTime;

    /**
     * 审核状态（0待审核 1已通过 2未通过）
     */
    @ApiModelProperty("审核状态")
    @TableField(value = "audit_status")
    private AuditStatusEnum auditStatus;

    @ApiModelProperty("有效期限")
    @TableField(value = "expiration_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expirationDate;

    /**
     * 审核人ID（关联用户表）
     */
    @ApiModelProperty("审核人ID")
    @TableField(value = "reviewer_id")
    private Long reviewerId;

    /**
     * 审核时间
     */
    @ApiModelProperty("审核时间")
    @TableField(value = "review_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date reviewTime;

    /**
     * 软删除标识:0-未删除,1-已删除
     */
    @ApiModelProperty("软删除标识:0-未删除,1-已删除")
    @TableField(value = "delete_flag")
    @TableLogic
    private Integer deleteFlag;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    @TableField(value = "create_by")
    private String createBy;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    @TableField(value = "update_by")
    private String updateBy;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    @TableField(value = "remark")
    private String remark;

}