package com.shengyu.business.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.shengyu.common.core.domain.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 农机服务预定表
 *
 * @TableName t_business_machine_book
 */
@TableName(value = "t_business_machine_book")
@Data
@ApiModel(value = "农机服务预定表", description = "农机服务预定表")
public class MachineBook extends TenantEntity {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 预定编号
     */
    @ApiModelProperty("预定编号")
    @TableField(value = "book_no")
    private String bookNo;

    /**
     * 用户ID
     */
    @ApiModelProperty("用户ID")
    @TableField(value = "book_user_id")
    private Long bookUserId;

    /**
     * 服务类型
     */
    @ApiModelProperty("服务类型")
    @TableField(value = "serve_type")
    private String serveType;

    /**
     * 农机服务ID
     */
    @ApiModelProperty("农机服务ID")
    @TableField(value = "serve_id")
    private Long serveId;

    @ApiModelProperty("作物类型")
    @TableField(value = "crop_type")
    private String cropType;

    /**
     * 预定状态（booking预约中，processing处理中，completed已完成）
     */
    @ApiModelProperty("预定状态（booking预约中，processing处理中，completed已完成）")
    @TableField(value = "book_status")
    private String bookStatus;



    /**
     * 联系人姓名
     */
    @ApiModelProperty("联系人姓名")
    @TableField(value = "contact_name")
    private String contactName;

    /**
     * 联系电话
     */
    @ApiModelProperty("联系电话")
    @TableField(value = "phone")
    private String phone;

    /**
     * 身份证号码
     */
    @ApiModelProperty("身份证号码")
    @TableField(value = "id_card")
    private String idCard;

    /**
     * 地块数量
     */
    @ApiModelProperty("地块数量")
    @TableField(value = "plot_count")
    private BigDecimal plotCount;

    /**
     * 服务面积(亩)
     */
    @ApiModelProperty("服务面积(亩)")
    @TableField(value = "area")
    private BigDecimal area;

    /**
     * 部门ID
     */
    @ApiModelProperty("部门ID")
    @TableField(value = "dept_id")
    private Long deptId;

    /**
     * 详细地址
     */
    @ApiModelProperty("详细地址")
    @TableField(value = "address_detail")
    private String addressDetail;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("有效期限")
    @TableField(value = "expiration_date")
    private Date expirationDate;

    /**
     * 软删除标识:0-未删除,1-已删除
     */
    @ApiModelProperty("软删除标识:0-未删除,1-已删除")
    @TableField(value = "delete_flag")
    @TableLogic
    private Integer deleteFlag;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}