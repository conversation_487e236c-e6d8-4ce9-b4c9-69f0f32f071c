package com.shengyu.business.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.shengyu.business.domain.MachineBook;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 农机预约记录DTO（包含字典名称）
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "农机预约记录DTO")
public class MachineBookDto extends MachineBook {
    @ApiModelProperty("服务类型名称")
    private String serveTypeName;

    @ApiModelProperty("作物类型名称")
    private String cropTypeName;

    @ApiModelProperty("预约状态名称")
    private String bookStatusName;

    @ApiModelProperty("部门名称")
    private String deptName;
}

