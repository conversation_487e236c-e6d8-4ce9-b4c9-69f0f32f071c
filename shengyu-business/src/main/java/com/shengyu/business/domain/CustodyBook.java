package com.shengyu.business.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.shengyu.business.enums.CustodyCategoryEnum;
import com.shengyu.common.core.domain.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 托管预定表
 *
 * @TableName t_custody_book
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "托管预定表")
@TableName(value = "t_business_custody_book")
public class CustodyBook extends TenantEntity {
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("预定编号")
    @TableField(value = "book_no")
    private String bookNo;

    @ApiModelProperty("预定用户ID")
    @TableField(value = "book_user_id")
    private Long bookUserId;

    @ApiModelProperty("托管类型(CUSTODY_FULL/CUSTODY_SINGLE)")
    @TableField(value = "custody_category")
    private CustodyCategoryEnum custodyCategory;

    @ApiModelProperty("托管服务ID")
    @TableField(value = "custody_id")
    private Long custodyId;

    @ApiModelProperty("作物类型")
    @TableField(value = "crop_type")
    private String cropType;

    @ApiModelProperty("作业项ID")
    @TableField(value = "item_id")
    private String itemId;

    @ApiModelProperty("预定状态（booking预约中，processing处理中，completed已完成）")
    @TableField(value = "book_status")
    private String bookStatus;

    @ApiModelProperty("托管单价")
    @TableField(value = "unit_price")
    private BigDecimal unitPrice;

    @ApiModelProperty("托管单位（如亩、次）")
    @TableField(value = "unit")
    private String unit;

    @ApiModelProperty("托管总价")
    @TableField(value = "total_price")
    private BigDecimal totalPrice;

    @ApiModelProperty("联系人姓名")
    @TableField(value = "contact_name")
    private String contactName;

    @ApiModelProperty("联系电话")
    @TableField(value = "phone")
    private String phone;

    @ApiModelProperty("身份证号码")
    @TableField(value = "id_card")
    private String idCard;

    @ApiModelProperty("地块数量")
    @TableField(value = "plot_count")
    private BigDecimal plotCount;

    @ApiModelProperty("托管面积(亩)")
    @TableField(value = "area")
    private BigDecimal area;

    @ApiModelProperty("部门ID")
    @TableField(value = "dept_id")
    private Long deptId;

    @ApiModelProperty("详细地址")
    @TableField(value = "address_detail")
    private String addressDetail;

    @ApiModelProperty("软删除标识:0-未删除,1-已删除")
    @TableField(value = "delete_flag")
    @TableLogic
    private Integer deleteFlag;

    @ApiModelProperty("服务商")
    @TableField(value = "service_provider_id")
    private Long serviceProviderId;
}