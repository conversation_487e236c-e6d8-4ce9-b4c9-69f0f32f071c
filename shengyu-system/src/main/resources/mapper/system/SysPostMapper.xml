<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shengyu.system.mapper.SysPostMapper">

    <resultMap type="SysPost" id="SysPostResult">
        <id property="postId" column="post_id"/>
        <result property="postCode" column="post_code"/>
        <result property="postName" column="post_name"/>
        <result property="postSort" column="post_sort"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>

    <sql id="selectPostVo">
        select t.* from sys_post t
    </sql>

    <sql id="whereCommonQ">
        <if test="q.postCode != null and q.postCode != ''">
            AND t.post_code like concat('%', #{q.postCode}, '%')
        </if>
        <if test="q.tenantId != null and q.tenantId != ''">
            AND t.tenant_id = #{q.tenantId}
        </if>
        <if test="q.status != null and q.status != ''">
            AND t.status = #{q.status}
        </if>
        <if test="q.postName != null and q.postName != ''">
            AND t.post_name like concat('%', #{q.postName}, '%')
        </if>
    </sql>

    <select id="selectPostList" parameterType="SysPost" resultMap="SysPostResult">
        <include refid="selectPostVo"/>
        <where>
            <if test="postCode != null and postCode != ''">
                AND t.post_code like concat('%', #{postCode}, '%')
            </if>
            <if test="tenantId != null and tenantId != ''">
                AND t.tenant_id = #{tenantId}
            </if>
            <if test="status != null and status != ''">
                AND t.status = #{status}
            </if>
            <if test="postName != null and postName != ''">
                AND t.post_name like concat('%', #{postName}, '%')
            </if>
        </where>
    </select>

    <select id="selectPage" resultMap="SysPostResult">
        <include refid="selectPostVo"/>
        <where>
            <include refid="whereCommonQ"/>
        </where>
    </select>

    <select id="selectPostAll" resultMap="SysPostResult" parameterType="String">
        <include refid="selectPostVo"/>
        where 1=1
        <if test="tenantId != null and tenantId != ''">
            AND t.tenant_id = #{tenantId}
        </if>
    </select>

    <select id="selectPostById" parameterType="Long" resultMap="SysPostResult">
        <include refid="selectPostVo"/>
        where t.post_id = #{postId}
    </select>

    <select id="selectPostListByUserId" parameterType="Long" resultType="String">
		select p.post_id
        from sys_post p
	        left join sys_user_post up on up.post_id = p.post_id
	        left join sys_user u on u.user_id = up.user_id
	    where u.user_id = #{userId}
	</select>

    <select id="selectPostsByUserName" parameterType="String" resultMap="SysPostResult">
		select p.post_id, p.post_name, p.post_code
		from sys_post p
			 left join sys_user_post up on up.post_id = p.post_id
			 left join sys_user u on u.user_id = up.user_id
		where u.user_name = #{userName}
	</select>

    <select id="checkPostNameUnique" parameterType="String" resultMap="SysPostResult">
        <include refid="selectPostVo"/>
        where t.post_name=#{postName} and t.tenant_id=#{tenantId} limit 1
    </select>

    <select id="checkPostCodeUnique" parameterType="String" resultMap="SysPostResult">
        <include refid="selectPostVo"/>
        where t.post_code=#{postCode} and t.tenant_id=#{tenantId} limit 1
    </select>

    <update id="updatePost" parameterType="SysPost">
        update sys_post
        <set>
            <if test="postCode != null and postCode != ''">post_code = #{postCode},</if>
            <if test="postName != null and postName != ''">post_name = #{postName},</if>
            <if test="postSort != null and postSort != ''">post_sort = #{postSort},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = sysdate()
        </set>
        where post_id = #{postId}
    </update>

    <insert id="insertPost" parameterType="SysPost" useGeneratedKeys="true" keyProperty="postId">
        insert into sys_post(
        <if test="postId != null and postId != 0">post_id,</if>
        <if test="postCode != null and postCode != ''">post_code,</if>
        <if test="postName != null and postName != ''">post_name,</if>
        <if test="postSort != null and postSort != ''">post_sort,</if>
        <if test="status != null and status != ''">status,</if>
        <if test="remark != null and remark != ''">remark,</if>
        <if test="createBy != null and createBy != ''">create_by,</if>
        <if test="tenantId != null and tenantId != ''">tenant_id,</if>
        create_time
        )values(
        <if test="postId != null and postId != 0">#{postId},</if>
        <if test="postCode != null and postCode != ''">#{postCode},</if>
        <if test="postName != null and postName != ''">#{postName},</if>
        <if test="postSort != null and postSort != ''">#{postSort},</if>
        <if test="status != null and status != ''">#{status},</if>
        <if test="remark != null and remark != ''">#{remark},</if>
        <if test="createBy != null and createBy != ''">#{createBy},</if>
        <if test="tenantId != null and tenantId != ''">#{tenantId},</if>
        sysdate()
        )
    </insert>

    <delete id="deletePostById" parameterType="Long">
		delete from sys_post where post_id = #{postId}
	</delete>

    <delete id="deletePostByIds" parameterType="Long">
        delete from sys_post where post_id in
        <foreach collection="array" item="postId" open="(" separator="," close=")">
            #{postId}
        </foreach>
    </delete>

    <select id="getPostCode" resultType="String" parameterType="Long">
        select p.post_code
        from sys_post p
	        left join sys_user_post up on up.post_id = p.post_id
	        left join sys_user u on u.user_id = up.user_id
	    where u.user_id = #{userId}
    </select>
</mapper> 