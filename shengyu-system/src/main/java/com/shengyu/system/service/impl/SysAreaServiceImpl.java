package com.shengyu.system.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shengyu.common.constant.Constants;
import com.shengyu.common.core.redis.RedisCache;
import com.shengyu.common.utils.StringUtils;
import com.shengyu.system.domain.SysArea;
import com.shengyu.system.domain.vo.AreaTreeVo;
import com.shengyu.system.mapper.SysAreaMapper;
import com.shengyu.system.service.ISysAreaService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 省市县管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-07-06
 */
@Service
public class SysAreaServiceImpl extends ServiceImpl<SysAreaMapper, SysArea> implements ISysAreaService {
    @Resource
    private SysAreaMapper sysAreaMapper;
    @Resource
    private RedisCache redisCache;

    /**
     * description: 初始化到redis缓存中
     * version: 1.0
     * date: 2021/7/7 15:03
     *
     * @param
     * @return void
     * @author: lwy
     */
    @Override
    public void initAreaToRedis() {
        //查询第一级
        LambdaQueryWrapper<SysArea> query = new LambdaQueryWrapper();
        String defaultCode = redisCache.getCacheObject(Constants.SYS_CONFIG_KEY + "sys.area.code");
        if (StringUtils.isEmpty(defaultCode)) {
            //parentCode=100000代表的是中国
            query.eq(SysArea::getLevel, 1);
        } else {
            query.in(SysArea::getCode, Arrays.asList(defaultCode.split(",")));
        }
        List<SysArea> areaList = sysAreaMapper.selectList(query);
        List<AreaTreeVo> areaTreeList = new ArrayList<>();
        for (SysArea area : areaList) {
            AreaTreeVo areaTree = new AreaTreeVo(area);
            List<AreaTreeVo> sonsList = getSons(area.getCode());
            areaTree.setChildren(sonsList);
            areaTreeList.add(areaTree);
        }
        redisCache.setCacheObject(Constants.AREA, areaTreeList);
    }

    /**
     * description: 递归查询子集
     * version: 1.0
     * date: 2021/7/7 15:03
     *
     * @param
     * @return void
     * @author: lwy
     */
    private List<AreaTreeVo> getSons(String parentCode) {
        List<AreaTreeVo> children = new ArrayList();
        LambdaQueryWrapper<SysArea> query = new LambdaQueryWrapper();
        query.eq(SysArea::getParentCode, parentCode);
        List<SysArea> areaList = sysAreaMapper.selectList(query);
        for (SysArea area : areaList) {
            AreaTreeVo areaTree = new AreaTreeVo(area);
            if (area.getLevel() < 3) {
                List<AreaTreeVo> sonsList = getSons(area.getCode());
                areaTree.setChildren(sonsList);
            }
            children.add(areaTree);
        }
        return children;
    }


    /**
     * 查询省市县管理
     *
     * @param id 省市县管理ID
     * @return 省市县管理
     */
    @Override
    public SysArea selectSysAreaById(Long id) {
        return sysAreaMapper.selectSysAreaById(id);
    }

    /**
     * 查询省市县管理列表
     *
     * @param sysArea 省市县管理
     * @return 省市县管理
     */
    @Override
    public List<SysArea> selectSysAreaList(SysArea sysArea) {
        return sysAreaMapper.selectSysAreaList(sysArea);
    }

    /**
     * 分页查询省市县管理
     *
     * @param page    分页参数
     * @param sysArea 查询条件
     * @return 分页结果
     */
    @Override
    public IPage<SysArea> selectPage(Page<SysArea> page, SysArea sysArea) {
        return baseMapper.selectPage(page, sysArea);
    }

    /**
     * 新增省市县管理
     *
     * @param sysArea 省市县管理
     * @return 结果
     */
    @Override
    public int insertSysArea(SysArea sysArea) {
        return sysAreaMapper.insertSysArea(sysArea);
    }

    /**
     * 修改省市县管理
     *
     * @param sysArea 省市县管理
     * @return 结果
     */
    @Override
    public int updateSysArea(SysArea sysArea) {
        return sysAreaMapper.updateSysArea(sysArea);
    }

    /**
     * 批量删除省市县管理
     *
     * @param ids 需要删除的省市县管理ID
     * @return 结果
     */
    @Override
    public int deleteSysAreaByIds(Long[] ids) {
        return sysAreaMapper.deleteSysAreaByIds(ids);
    }

    /**
     * 删除省市县管理信息
     *
     * @param id 省市县管理ID
     * @return 结果
     */
    @Override
    public int deleteSysAreaById(Long id) {
        return sysAreaMapper.deleteSysAreaById(id);
    }
}
