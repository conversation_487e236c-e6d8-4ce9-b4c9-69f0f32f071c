package com.shengyu.system.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.shengyu.common.core.domain.entity.SysDictData;
import com.shengyu.common.core.domain.model.DictTypeModel;
import com.shengyu.common.utils.DictUtils;
import com.shengyu.common.utils.StringUtils;
import com.shengyu.system.mapper.SysDictDataMapper;
import com.shengyu.system.service.ISysDictDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 字典 业务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysDictDataServiceImpl implements ISysDictDataService {
    @Autowired
    private SysDictDataMapper dictDataMapper;

    /**
     * 根据条件分页查询字典数据
     *
     * @param dictData 字典数据信息
     * @return 字典数据集合信息
     */
    @Override
    public List<SysDictData> selectDictDataList(SysDictData dictData) {
        return dictDataMapper.selectDictDataList(dictData);
    }

    @Override
    public IPage<SysDictData> selectPage(Page<SysDictData> page, SysDictData dictData) {
        return dictDataMapper.selectPage(page, dictData);
    }

    /**
     * 根据字典类型和字典键值查询字典数据信息
     *
     * @param dictType  字典类型
     * @param dictValue 字典键值
     * @return 字典标签
     */
    @Override
    public String selectDictLabel(String dictType, String dictValue) {
        return dictDataMapper.selectDictLabel(dictType, dictValue);
    }

    /**
     * 根据字典数据ID查询信息
     *
     * @param dictCode 字典数据ID
     * @return 字典数据
     */
    @Override
    public SysDictData selectDictDataById(Long dictCode) {
        return dictDataMapper.selectDictDataById(dictCode);
    }

    /**
     * 批量删除字典数据信息
     *
     * @param dictCodes 需要删除的字典数据ID
     * @return 结果
     */
    @Override
    public void deleteDictDataByIds(Long[] dictCodes) {
        for (Long dictCode : dictCodes) {
            SysDictData data = selectDictDataById(dictCode);
            dictDataMapper.deleteDictDataById(dictCode);
            List<SysDictData> dictDatas = dictDataMapper.selectDictDataByType(data.getDictType());
            DictUtils.setDictCache(data.getDictType(), dictDatas);
        }
    }

    /**
     * 新增保存字典数据信息
     *
     * @param data 字典数据信息
     * @return 结果
     */
    @Override
    public int insertDictData(SysDictData data) {
        int row = dictDataMapper.insertDictData(data);
        if (row > 0) {
            List<SysDictData> dictDatas = dictDataMapper.selectDictDataByType(data.getDictType());
            DictUtils.setDictCache(data.getDictType(), dictDatas);
        }
        return row;
    }

    /**
     * 修改保存字典数据信息
     *
     * @param data 字典数据信息
     * @return 结果
     */
    @Override
    public int updateDictData(SysDictData data) {
        int row = dictDataMapper.updateDictData(data);
        if (row > 0) {
            List<SysDictData> dictDatas = dictDataMapper.selectDictDataByType(data.getDictType());
            DictUtils.setDictCache(data.getDictType(), dictDatas);
        }
        return row;
    }

    private List<SysDictData> selectDictDataByType(String dictType) {
        List<SysDictData> dictDatas = DictUtils.getDictCache(dictType);
        if (StringUtils.isNotEmpty(dictDatas)) {
            return dictDatas;
        }
        dictDatas = dictDataMapper.selectDictDataByType(dictType);
        if (StringUtils.isNotEmpty(dictDatas)) {
            DictUtils.setDictCache(dictType, dictDatas);
            return dictDatas;
        }
        return null;
    }

    @Override
    public List<DictTypeModel> getDebtType() {
        List<SysDictData> data = selectDictDataByType("debtType");
        if (StringUtils.isNotEmpty(data)) {
            List<DictTypeModel> res = new ArrayList<>();
            data.forEach(d -> {
                DictTypeModel m = new DictTypeModel();
                m.setId(d.getDictValue().replaceAll("-", ""));
                if (d.getDictValue().contains("-")) {//有父节点
                    int lastInx = d.getDictValue().lastIndexOf("-");
                    m.setParentId(d.getDictValue().substring(0, lastInx).replaceAll("-", ""));
                } else {
                    m.setParentId("0");
                }
                m.setName(d.getDictLabel());
                res.add(m);
            });
            return buildTree(res);
        }
        return null;
    }

    @Override
    public List<DictTypeModel> getContractType() {
        List<SysDictData> data = selectDictDataByType("contractType");
        if (StringUtils.isNotEmpty(data)) {
            List<DictTypeModel> res = new ArrayList<>();
            DictTypeModel t1 = new DictTypeModel();
            t1.setId("1");
            t1.setName("收入类合同");
            t1.setParentId("0");
            List<DictTypeModel> c1 = new ArrayList<>();
            t1.setChildren(c1);
            res.add(t1);
            DictTypeModel t2 = new DictTypeModel();
            t2.setId("2");
            t2.setParentId("0");
            t2.setName("支出类合同");
            List<DictTypeModel> c2 = new ArrayList<>();
            t2.setChildren(c2);
            res.add(t2);
            data.forEach(d -> {
                DictTypeModel m = new DictTypeModel();
                m.setId(d.getDictValue());
                String parentId = d.getDictValue().split("_")[0];
                m.setParentId(parentId);
                m.setName(d.getDictLabel());
                m.setLeaf(true);
                if("1".equals(parentId)){
                    c1.add(m);
                }else{
                    c2.add(m);
                }
            });
            return res;
        }
        return null;
    }

    @Override
    public List<DictTypeModel> getLiabilitiesType() {
        return commonGetLiabilities("subjectCountMapType");
    }

    @Override
    public List<DictTypeModel> getNewLiabilitiesType() {
        return commonGetLiabilities("newSubjectCountMapType");
    }

    private List<DictTypeModel> commonGetLiabilities(String type) {
        List<SysDictData> data = selectDictDataByType(type);
        if (StringUtils.isNotEmpty(data)) {
            List<DictTypeModel> res = new ArrayList<>();
            data.forEach(d -> {
                DictTypeModel m = new DictTypeModel();
                String str[] = d.getDictValue().split("_");
                if (str[0].equals(str[3])) {//不处理
                    return;
                }
                if ("0".equals(str[3])) {
                    m.setId(str[0]);
                } else {
                    m.setId(d.getDictValue());
                    m.setLeaf(true);
                }
                m.setParentId(str[3]);
                m.setName(d.getDictLabel());
                res.add(m);
            });
            return buildTree(res);
        }
        return null;
    }

    private List<DictTypeModel> buildTree(List<DictTypeModel> debtTypes) {
        List<DictTypeModel> returnList = new ArrayList<>();
        List<String> tempList = new ArrayList<>();
        debtTypes = debtTypes.stream().sorted(Comparator.comparing(DictTypeModel::getId)).collect(Collectors.toList());
        for (DictTypeModel d : debtTypes) {
            tempList.add(d.getId());
        }
        for (Iterator<DictTypeModel> iterator = debtTypes.iterator(); iterator.hasNext(); ) {
            DictTypeModel debt = iterator.next();
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(debt.getParentId())) {
                recursionFn(debtTypes, debt);
                returnList.add(debt);
            }
        }
        if (returnList.isEmpty()) {
            returnList = debtTypes;
        }
        return returnList;
    }

    /**
     * 递归列表
     *
     * @param list
     * @param t
     */
    private void recursionFn(List<DictTypeModel> list, DictTypeModel t) {
        // 得到子节点列表
        List<DictTypeModel> childList = getChildList(list, t);
        t.setChildren(childList);
        for (DictTypeModel tChild : childList) {
            if (hasChild(list, tChild)) {
                recursionFn(list, tChild);
            }
        }
    }

    /**
     * 得到子节点列表
     */
    private List<DictTypeModel> getChildList(List<DictTypeModel> list, DictTypeModel t) {
        List<DictTypeModel> tlist = new ArrayList<>();
        Iterator<DictTypeModel> it = list.iterator();
        while (it.hasNext()) {
            DictTypeModel n = it.next();
            if (n.getParentId().equals(t.getId())) {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<DictTypeModel> list, DictTypeModel t) {
        return getChildList(list, t).size() > 0;
    }
}
