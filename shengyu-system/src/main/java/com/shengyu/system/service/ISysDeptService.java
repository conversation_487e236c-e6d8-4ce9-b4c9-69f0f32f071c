package com.shengyu.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.shengyu.common.core.domain.TreeSelect;
import com.shengyu.common.core.domain.entity.SysDept;
import com.shengyu.system.domain.model.ScheduleParam;
import com.shengyu.system.domain.vo.AccountingVo;
import com.shengyu.system.domain.vo.CountScheduleVo;
import com.shengyu.system.domain.vo.ScheduleDetailsVo;
import com.shengyu.system.domain.vo.SupervisionVo;

import java.util.List;
import java.util.Map;

/**
 * 部门管理 服务层
 *
 * <AUTHOR>
 */
public interface ISysDeptService {
    /**
     * 查询部门管理数据
     *
     * @param dept 部门信息
     * @return 部门信息集合
     */
    public List<SysDept> selectDeptList(SysDept dept);

    /**
     * 分页查询部门管理数据（MyBatis-Plus）
     *
     * @param page 分页参数
     * @param dept 查询条件
     * @return 分页结果
     */
    IPage<SysDept> selectPage(Page<SysDept> page, SysDept dept);

    /**
     * 构建前端所需要树结构
     *
     * @param depts 部门列表
     * @return 树结构列表
     */
    public List<SysDept> buildDeptTree(List<SysDept> depts);

    /**
     * 构建前端所需要下拉树结构
     *
     * @param depts 部门列表
     * @return 下拉树结构列表
     */
    public List<TreeSelect> buildDeptTreeSelect(List<SysDept> depts);

    /**
     * 根据角色ID查询部门树信息
     *
     * @param roleId 角色ID
     * @return 选中部门列表
     */
    public List<Integer> selectDeptListByRoleId(Long roleId);

    /**
     * 根据部门ID查询信息
     *
     * @param deptId 部门ID
     * @return 部门信息
     */
    public SysDept selectDeptById(Long deptId);

    /**
     * 根据ID查询所有子部门（正常状态）
     *
     * @param deptId 部门ID
     * @return 子部门数
     */
    public int selectNormalChildrenDeptById(Long deptId);

    /**
     * 是否存在部门子节点
     *
     * @param deptId 部门ID
     * @return 结果
     */
    public boolean hasChildByDeptId(Long deptId);

    /**
     * 查询部门是否存在用户
     *
     * @param deptId 部门ID
     * @return 结果 true 存在 false 不存在
     */
    public boolean checkDeptExistUser(Long deptId);

    /**
     * 校验部门名称是否唯一
     *
     * @param dept 部门信息
     * @return 结果
     */
    public String checkDeptCodeUnique(SysDept dept);

    /**
     * 新增保存部门信息
     *
     * @param dept 部门信息
     * @return 结果
     */
    public int insertDept(SysDept dept);

    /**
     * 修改保存部门信息
     *
     * @param dept 部门信息
     * @return 结果
     */
    public int updateDept(SysDept dept);

    /**
     * 删除部门管理信息
     *
     * @param deptId 部门ID
     * @return 结果
     */
    public int deleteDeptById(Long deptId);

    //根据部门id查询该部门子节点数据
    List<SysDept> selectChildrenDeptById(Long deptId);

    //导入数据
    String importDept(List<SysDept> list, String operName);

    List<SysDept> getTownList();

    //根据行政区划获取县级deptId
    SysDept getDeptIdByAreaId(Long areaId);

    //进度监管统计
    List<SupervisionVo> supervision(ScheduleParam param);

    //财务组织机构统计
    List<CountScheduleVo> countSchedule(ScheduleParam param);

    //财务组织机构进度详情
    List<ScheduleDetailsVo> scheduleDetails(ScheduleParam param);

    List<SysDept> selectBuildDeptList(ScheduleParam param);

    List<SysDept> selectChildListForScope(Long deptId);

    String getAllDeptName(Long deptId);

    List<SysDept> getAllDept();

    String getTownAndVillage(Long deptId);

    String getAutoName(Long deptId);

    List<Map<String, Object>> getCityDept();

    List<AccountingVo> getDeptAccountingList(ScheduleParam param);
}
