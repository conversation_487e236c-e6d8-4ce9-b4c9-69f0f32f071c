package com.shengyu.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.shengyu.common.core.domain.dto.SysUserDto;
import com.shengyu.common.core.domain.entity.SysUser;
import com.shengyu.common.core.domain.model.LoginUser;

import java.util.List;

/**
 * 用户 业务层
 *
 * <AUTHOR>
 */
public interface ISysUserService {
    /**
     * 根据条件分页查询用户列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    public List<SysUser> selectUserList(SysUser user);

    /**
     * 根据条件分页查询用户列表（MyBatis-Plus）
     *
     * @param page 分页参数
     * @param user 查询条件
     * @return 分页结果
     */
    IPage<SysUser> selectPage(Page<SysUser> page, SysUser user);

    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    public SysUser selectUserByUserName(String userName);

    /**
     * 通过用户名查询用户
     *
     * @param phone 手机号
     * @return 用户对象信息
     */
    public SysUser selectUserByPhone(String phone);

    /**
     * 通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    public SysUser selectUserById(Long userId);

    /**
     * 根据用户ID查询用户所属角色组
     *
     * @param userName 用户名
     * @return 结果
     */
    public String selectUserRoleGroup(String userName);

    /**
     * 根据用户ID查询用户所属岗位组
     *
     * @param userName 用户名
     * @return 结果
     */
    public String selectUserPostGroup(String userName);

    /**
     * 校验用户名称是否唯一
     *
     * @param userName 用户名称
     * @return 结果
     */
    public String checkUserNameUnique(String userName);

    /**
     * 校验手机号码是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    public String checkPhoneUnique(SysUser user);

    /**
     * 校验email是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    public String checkEmailUnique(SysUser user);

    public String checkIdCardUnique(SysUser user);

    /**
     * 校验用户是否允许操作
     *
     * @param user 用户信息
     */
    public void checkUserAllowed(SysUser user);

    /**
     * 新增用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    public int insertUser(SysUser user, boolean isSync);

    /**
     * 修改用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    public int updateUser(SysUser user,boolean isSync);

    /**
     * 用户授权角色
     *
     * @param userId  用户ID
     * @param roleIds 角色组
     */
    public void insertUserAuth(Long userId, Long[] roleIds);

    /**
     * 修改用户状态
     *
     * @param user 用户信息
     * @return 结果
     */
    public int updateUserStatus(SysUser user);

    /**
     * 修改用户基本信息
     *
     * @param user 用户信息
     * @return 结果
     */
    public int updateUserProfile(SysUser user,boolean isSync);

    /**
     * 修改用户头像
     *
     * @param userName 用户名
     * @param avatar   头像地址
     * @return 结果
     */
    public boolean updateUserAvatar(String userName, String avatar);

    /**
     * 重置用户密码
     *
     * @param user 用户信息
     * @return 结果
     */
    public int resetPwd(SysUser user);

    /**
     * @Description: 批量重置密码
     * @Param: [ids, password]
     * @return: int
     * @Author: SS
     * @Date: 2023/2/17
     */
    int batchResetPwd(Long[] ids, String password);

    /**
     * 重置用户密码
     *
     * @param userName 用户名
     * @param password 密码
     * @return 结果
     */
    public int resetUserPwd(String userName, String password);

    /**
     * 通过用户ID删除用户
     *
     * @param userId 用户ID
     * @return 结果
     */
    public int deleteUserById(Long userId, boolean isSync);

    /**
     * 批量删除用户信息
     *
     * @param userIds 需要删除的用户ID
     * @return 结果
     */
    public int deleteUserByIds(Long[] userIds);

    /**
     * 导入用户数据
     *
     * @param userList        用户数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importUser(List<SysUser> userList, Boolean isUpdateSupport, String operName);

    /**
     * description: 根据关键字查询用户
     * version: 1.0
     * date: 2021/8/24 14:51
     *
     * @param user
     * @return java.util.List<com.shengyu.common.core.domain.entity.SysUser>
     * @author: lwy
     */
    List<SysUser> selectByKeyWord(SysUser user);

    //根据id获取数据
    SysUserDto selectById(Long userId);

    //创建用户对象
    LoginUser createAppLoginUser(String userKey, String token);

    int updateUserToken(String token, String userName);

    //修改appUser
    int updateAppUser(SysUser user);

    boolean updateUserPhone(String userName, String phone);

    //根据角色key获取该角色所有用户
    List<String> selectListByRoleKey(String roleKey, Long deptId);

    //根据身份证号获取用户
    public SysUser getUserByIdCard(String idCard);

    //根据组织机构查询用户
    List<SysUser> selectUserByDepts(Long[] deptIds);

    //根据岗位获取用户,为空返回默认当前登录用户
    SysUser getUserByPost(Long deptId, String post);

    //根据岗位获取用户,为空返回默认当前登录用户
    SysUser getUserByPostNoDefVal(Long deptId, String post);

    //根据岗位获取拥有管理该组织机构的用户
    SysUser getUserByPostForParent(Long deptId, String post);

    int authorize(Long id,String secStr);

}
