package com.shengyu.system.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shengyu.common.core.domain.BaseEntity;
import com.shengyu.system.domain.SysArea;
import com.shengyu.system.domain.vo.AreaTreeVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 省市县管理Mapper接口
 *
 * <AUTHOR>
 * @date 2021-07-06
 */
public interface SysAreaMapper extends BaseMapper<SysArea> {
    /**
     * 查询省市县管理
     *
     * @param id 省市县管理ID
     * @return 省市县管理
     */
    public SysArea selectSysAreaById(Long id);

    /**
     * 查询省市县管理列表
     *
     * @param sysArea 省市县管理
     * @return 省市县管理集合
     */
    public List<SysArea> selectSysAreaList(SysArea sysArea);

    /**
     * 分页查询省市县管理
     * @param page 分页参数
     * @param sysArea 查询条件
     * @return 分页结果
     */
    IPage<SysArea> selectPage(Page<SysArea> page, @Param("q") SysArea sysArea);

    /**
     * 新增省市县管理
     *
     * @param sysArea 省市县管理
     * @return 结果
     */
    public int insertSysArea(SysArea sysArea);

    /**
     * 修改省市县管理
     *
     * @param sysArea 省市县管理
     * @return 结果
     */
    public int updateSysArea(SysArea sysArea);

    /**
     * 删除省市县管理
     *
     * @param id 省市县管理ID
     * @return 结果
     */
    public int deleteSysAreaById(Long id);

    /**
     * 批量删除省市县管理
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteSysAreaByIds(Long[] ids);

    /**
     * @Description: 动态获取树列表
     * @Param: [level]
     * @return: java.util.List<com.shengyu.system.domain.vo.AreaTreeVo>
     * @Author: SS
     * @Date: 2023/2/16
     */
    List<AreaTreeVo> selectActiveTree(@Param("level") Integer level, @Param("defaultCode") String defaultCode);

    //获取组织结构树
    List<AreaTreeVo> selectActiveLevelTree(@Param("entity") BaseEntity entity, @Param("defaultCode") String defaultCode);
}
