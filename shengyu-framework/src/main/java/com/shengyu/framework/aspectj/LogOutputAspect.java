package com.shengyu.framework.aspectj;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;

import static com.alibaba.fastjson.JSON.toJSONString;

/**
 * 日志输出切面
 * 专门用于输出请求和响应的调试信息
 *
 * <AUTHOR>
 */
@Aspect
@Component
public class LogOutputAspect {
    private static final Logger logger = LoggerFactory.getLogger(LogOutputAspect.class);

    @Around("execution(* com.shengyu..controller..*(..))")
    public Object aroundLog(ProceedingJoinPoint joinPoint) throws Throwable {
        // 过滤掉@InitBinder方法
        if (isInitBinderMethod(joinPoint)) {
            return joinPoint.proceed();
        }
        
        logRequestInfo(joinPoint);
        
        long startTime = System.currentTimeMillis();
        try {
            Object result = joinPoint.proceed();
            logResponseInfo(result, System.currentTimeMillis() - startTime);
            return result;
        } catch (Throwable e) {
            logger.error("方法执行异常: {} - 执行时间: {}ms", e.getMessage(), System.currentTimeMillis() - startTime);
            throw e;
        }
    }

    private void logRequestInfo(ProceedingJoinPoint joinPoint) {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) return;
        
        HttpServletRequest request = attributes.getRequest();
        String fullUrl = request.getRequestURL().toString() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        
        logger.debug("请求地址: {} {}", request.getMethod(), fullUrl);
        if (request.getContentType() != null) {
            logger.debug("请求类型: {}", request.getContentType());
        }
        logger.debug("调用方法: {}.{}", joinPoint.getSignature().getDeclaringTypeName(), joinPoint.getSignature().getName());
        
        Object[] args = joinPoint.getArgs();
        if (args != null && args.length > 0) {
            for (int i = 0; i < args.length; i++) {
                if (args[i] != null) {
                    // 跳过Spring Security相关的包装对象，避免序列化异常
                    if (isSecurityWrapperObject(args[i])) {
                        logger.debug("参数[{}]: {} - [SKIPPED SECURITY WRAPPER OBJECT]", i, args[i].getClass().getSimpleName());
                        continue;
                    }
                    
                    try {
                        logger.debug("参数[{}]: {} - {}", i, args[i].getClass().getSimpleName(), toJSONString(args[i]));
                    } catch (Exception e) {
                        logger.error("参数[{}]: {} - {} (序列化异常: {})", i, args[i].getClass().getSimpleName(), args[i].toString(), e.getMessage());
                    }
                } else {
                    logger.debug("参数[{}]: null", i);
                }
            }
        } else {
            logger.debug("无参数");
        }
    }

    private void logResponseInfo(Object result, long duration) {
        if (result != null) {
            try {
                logger.debug("返回结果: {} - {}", result.getClass().getSimpleName(), toJSONString(result));
            } catch (Exception e) {
                logger.error("返回结果序列化异常: {} - {} (异常: {})", result.getClass().getSimpleName(), result.toString(), e.getMessage());
            }
        } else {
            logger.debug("返回结果: null");
        }
        logger.debug("执行时间: {}ms", duration);
    }

    /**
     * 判断是否为@InitBinder方法
     */
    private boolean isInitBinderMethod(ProceedingJoinPoint joinPoint) {
        try {
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            return method.isAnnotationPresent(InitBinder.class);
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 判断是否为Spring Security包装对象
     */
    private boolean isSecurityWrapperObject(Object obj) {
        String className = obj.getClass().getName();
        return className.contains("SecurityContextHolderAwareRequestWrapper") ||
               className.contains("Servlet3SecurityContextHolderAwareRequestWrapper") ||
               className.contains("HttpRequestWrapper") ||
               className.contains("ServletRequestWrapper");
    }
}