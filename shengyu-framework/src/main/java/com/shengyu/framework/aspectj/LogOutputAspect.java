package com.shengyu.framework.aspectj;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.regex.Pattern;

import static com.alibaba.fastjson.JSON.toJSONString;

/**
 * HTTP请求日志切面
 * <AUTHOR>
 */
@Aspect
@Component
public class LogOutputAspect {
    private static final Logger logger = LoggerFactory.getLogger(LogOutputAspect.class);
    private static final Pattern SENSITIVE = Pattern.compile("(?i)(password|pwd|token|secret|authorization)");

    @Around("execution(* com.shengyu..controller..*(..)) && !@annotation(org.springframework.web.bind.annotation.InitBinder)")
    public Object log(ProceedingJoinPoint point) throws Throwable {
        ServletRequestAttributes attrs = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attrs != null) {
            HttpServletRequest req = attrs.getRequest();
            String url = req.getRequestURL() + (req.getQueryString() != null ? "?" + req.getQueryString() : "");
            logger.debug("请求: {} {}", req.getMethod(), url);
            
            // 记录参数
            logParams(req, point);
        }
        
        long start = System.currentTimeMillis();
        try {
            Object result = point.proceed();
            logger.debug("响应: {} - {}ms", result != null ? toJSONString(result) : "null", System.currentTimeMillis() - start);
            return result;
        } catch (Exception e) {
            logger.error("异常: {} - {}ms", e.getMessage(), System.currentTimeMillis() - start);
            throw e;
        }
    }
    
    private void logParams(HttpServletRequest req, ProceedingJoinPoint point) {
        // URL参数
        Map<String, String[]> params = req.getParameterMap();
        if (!params.isEmpty()) {
            logger.debug("参数: {}", sanitize(params));
        }
        
        // 方法参数
        Object[] args = point.getArgs();
        if (args.length > 0) {
            for (int i = 0; i < args.length; i++) {
                if (args[i] != null && !isSkippable(args[i])) {
                    try {
                        logger.debug("参数[{}]: {}", i, toJSONString(args[i]));
                    } catch (Exception e) {
                        logger.debug("参数[{}]: {} (序列化失败)", i, args[i].getClass().getSimpleName());
                    }
                }
            }
        }
    }
    
    private Map<String, Object> sanitize(Map<String, String[]> params) {
        Map<String, Object> safe = new HashMap<>();
        params.forEach((k, v) -> {
            if (SENSITIVE.matcher(k).find()) {
                safe.put(k, "***");
            } else {
                safe.put(k, v.length == 1 ? v[0] : Arrays.asList(v));
            }
        });
        return safe;
    }
    
    private boolean isSkippable(Object obj) {
        String name = obj.getClass().getName();
        return name.contains("HttpServlet") || name.contains("InputStream") || 
               name.contains("OutputStream") || name.contains("MultipartFile") ||
               name.contains("DataBinder") || name.contains("BindingResult") ||
               name.contains("Model") || name.contains("RedirectAttributes");
    }
}