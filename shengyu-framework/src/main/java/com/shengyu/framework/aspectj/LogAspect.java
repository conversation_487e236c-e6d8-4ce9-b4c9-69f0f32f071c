package com.shengyu.framework.aspectj;

import com.alibaba.fastjson.JSON;
import com.shengyu.common.annotation.Log;
import com.shengyu.common.core.domain.model.LoginUser;
import com.shengyu.common.enums.BusinessStatus;
import com.shengyu.common.enums.HttpMethod;
import com.shengyu.common.utils.ServletUtils;
import com.shengyu.common.utils.StringUtils;
import com.shengyu.common.utils.ip.IpUtils;
import com.shengyu.common.utils.spring.SpringUtils;
import com.shengyu.framework.manager.AsyncManager;
import com.shengyu.framework.manager.factory.AsyncFactory;
import com.shengyu.framework.web.service.TokenService;
import com.shengyu.system.domain.SysOperLog;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.*;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.validation.BindingResult;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.HandlerMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.util.Collection;
import java.util.Iterator;
import java.util.Map;

import static com.alibaba.fastjson.JSON.toJSONString;

/**
 * 操作日志记录处理
 *
 * <AUTHOR>
 */
@Aspect
@Component
public class LogAspect {
    private static final Logger logger = LoggerFactory.getLogger(LogAspect.class);

    // 配置织入点
    @Pointcut("@annotation(com.shengyu.common.annotation.Log)")
    public void logPointCut() {
    }

    /**
     * 处理完请求后执行
     *
     * @param joinPoint 切点
     */
    @AfterReturning(pointcut = "logPointCut()", returning = "jsonResult")
    public void doAfterReturning(JoinPoint joinPoint, Object jsonResult) {
        handleLog(joinPoint, null, jsonResult);
    }

    /**
     * 拦截异常操作
     *
     * @param joinPoint 切点
     * @param e         异常
     */
    @AfterThrowing(value = "logPointCut()", throwing = "e")
    public void doAfterThrowing(JoinPoint joinPoint, Exception e) {
        handleLog(joinPoint, e, null);
    }

    protected void handleLog(final JoinPoint joinPoint, final Exception e, Object jsonResult) {
        try {
            // 获得注解
            Log controllerLog = getAnnotationLog(joinPoint);
            if (controllerLog == null) {
                return;
            }

            // 获取当前的用户
            LoginUser loginUser = SpringUtils.getBean(TokenService.class).getLoginUser(ServletUtils.getRequest());

            // 构建操作日志
            SysOperLog operLog = new SysOperLog();
            operLog.setStatus(BusinessStatus.SUCCESS.ordinal());
            
            // 设置基本信息
            String ip = IpUtils.getIpAddr(ServletUtils.getRequest());
            operLog.setOperIp(ip);
            operLog.setOperUrl(ServletUtils.getRequest().getRequestURI());
            operLog.setRequestMethod(ServletUtils.getRequest().getMethod());
            
            // 设置用户信息
            if (loginUser != null) {
                operLog.setOperName(loginUser.getUsername());
            }
            
            // 设置返回结果
            String resultStr = toJSONString(jsonResult);
            if (StringUtils.isNotEmpty(resultStr) && resultStr.length() > 1000) {
                resultStr = resultStr.substring(0, 1000);
            }
            operLog.setJsonResult(resultStr);
            
            // 设置方法信息
            String className = joinPoint.getTarget().getClass().getName();
            String methodName = joinPoint.getSignature().getName();
            operLog.setMethod(className + "." + methodName + "()");
            
            // 处理异常情况
            if (e != null) {
                operLog.setStatus(BusinessStatus.FAIL.ordinal());
                operLog.setErrorMsg(StringUtils.substring(e.getMessage(), 0, 2000));
            }
            
            // 处理注解参数
            getControllerMethodDescription(joinPoint, controllerLog, operLog);
            
            // 异步保存到数据库
            AsyncManager.me().execute(AsyncFactory.recordOper(operLog));
        } catch (Exception exp) {
            logger.error("==前置通知异常==");
            logger.error("异常信息:{}", exp.getMessage());
            exp.printStackTrace();
        }
    }

    /**
     * 获取注解中对方法的描述信息 用于Controller层注解
     *
     * @param log     日志
     * @param operLog 操作日志
     * @throws Exception
     */
    public void getControllerMethodDescription(JoinPoint joinPoint, Log log, SysOperLog operLog) throws Exception {
        // 设置action动作
        operLog.setBusinessType(log.businessType().ordinal());
        // 设置标题
        operLog.setTitle(log.title());
        // 设置操作人类别
        operLog.setOperatorType(log.operatorType().ordinal());
        // 是否需要保存request，参数和值
        if (log.isSaveRequestData()) {
            // 获取参数的信息，传入到数据库中。
            setRequestValue(joinPoint, operLog);
        }
    }

    /**
     * 获取请求的参数，放到log中
     *
     * @param operLog 操作日志
     * @throws Exception 异常
     */
    private void setRequestValue(JoinPoint joinPoint, SysOperLog operLog) throws Exception {
        String requestMethod = operLog.getRequestMethod();
        StringBuilder paramBuilder = new StringBuilder();
        
        if (HttpMethod.PUT.name().equals(requestMethod) || HttpMethod.POST.name().equals(requestMethod)) {
            // POST/PUT请求：获取请求体参数
            String params = argsArrayToString(joinPoint.getArgs());
            paramBuilder.append(params);
        } else if (HttpMethod.DELETE.name().equals(requestMethod)) {
            // DELETE请求：获取查询参数、路径变量和可能的请求体参数
            HttpServletRequest request = ServletUtils.getRequest();
            
            // 查询参数
            String queryString = request.getQueryString();
            if (StringUtils.isNotEmpty(queryString)) {
                paramBuilder.append("查询参数: ").append(queryString);
            }
            
            // 路径变量
            Map<?, ?> pathVariables = (Map<?, ?>) request.getAttribute(HandlerMapping.URI_TEMPLATE_VARIABLES_ATTRIBUTE);
            if (pathVariables != null && !pathVariables.isEmpty()) {
                if (paramBuilder.length() > 0) {
                    paramBuilder.append("; ");
                }
                paramBuilder.append("路径变量: ").append(pathVariables);
            }
            
            // 某些DELETE请求可能包含请求体参数
            String params = argsArrayToString(joinPoint.getArgs());
            if (StringUtils.isNotEmpty(params)) {
                if (paramBuilder.length() > 0) {
                    paramBuilder.append("; ");
                }
                paramBuilder.append("请求体参数: ").append(params);
            }
        } else {
            // GET请求：获取查询参数和路径变量
            HttpServletRequest request = ServletUtils.getRequest();
            
            // 查询参数
            String queryString = request.getQueryString();
            if (StringUtils.isNotEmpty(queryString)) {
                paramBuilder.append("查询参数: ").append(queryString);
            }
            
            // 路径变量
            Map<?, ?> pathVariables = (Map<?, ?>) request.getAttribute(HandlerMapping.URI_TEMPLATE_VARIABLES_ATTRIBUTE);
            if (pathVariables != null && !pathVariables.isEmpty()) {
                if (paramBuilder.length() > 0) {
                    paramBuilder.append("; ");
                }
                paramBuilder.append("路径变量: ").append(pathVariables);
            }
            
            // 如果没有查询参数和路径变量，尝试获取方法参数
            if (paramBuilder.length() == 0) {
                String params = argsArrayToString(joinPoint.getArgs());
                if (StringUtils.isNotEmpty(params)) {
                    paramBuilder.append("方法参数: ").append(params);
                }
            }
        }
        
        operLog.setOperParam(StringUtils.substring(paramBuilder.toString(), 0, 2000));
    }

    /**
     * 是否存在注解，如果存在就获取
     */
    private Log getAnnotationLog(JoinPoint joinPoint) throws Exception {
        Signature signature = joinPoint.getSignature();
        MethodSignature methodSignature = (MethodSignature) signature;
        Method method = methodSignature.getMethod();

        if (method != null) {
            return method.getAnnotation(Log.class);
        }
        return null;
    }

    /**
     * 参数拼装
     */
    private String argsArrayToString(Object[] paramsArray) {
        String params = "";
        if (paramsArray != null && paramsArray.length > 0) {
            for (int i = 0; i < paramsArray.length; i++) {
                if (StringUtils.isNotNull(paramsArray[i]) && !isFilterObject(paramsArray[i])) {
                    Object jsonObj = JSON.toJSON(paramsArray[i]);
                    params += jsonObj.toString() + " ";
                }
            }
        }
        return params.trim();
    }

    /**
     * 判断是否需要过滤的对象。
     *
     * @param o 对象信息。
     * @return 如果是需要过滤的对象，则返回true；否则返回false。
     */
    @SuppressWarnings("rawtypes")
    public boolean isFilterObject(final Object o) {
        Class<?> clazz = o.getClass();
        if (clazz.isArray()) {
            return clazz.getComponentType().isAssignableFrom(MultipartFile.class);
        } else if (Collection.class.isAssignableFrom(clazz)) {
            Collection collection = (Collection) o;
            for (Iterator iter = collection.iterator(); iter.hasNext(); ) {
                return iter.next() instanceof MultipartFile;
            }
        } else if (Map.class.isAssignableFrom(clazz)) {
            Map map = (Map) o;
            for (Iterator iter = map.entrySet().iterator(); iter.hasNext(); ) {
                Map.Entry entry = (Map.Entry) iter.next();
                return entry.getValue() instanceof MultipartFile;
            }
        }
        return o instanceof MultipartFile || o instanceof HttpServletRequest || o instanceof HttpServletResponse
                || o instanceof BindingResult;
    }
}
