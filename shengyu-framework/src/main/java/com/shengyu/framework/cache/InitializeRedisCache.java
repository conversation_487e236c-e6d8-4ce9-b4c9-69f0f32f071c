package com.shengyu.framework.cache;

import com.shengyu.common.config.CapitalConfig;
import com.shengyu.system.service.ISysAreaService;
import com.shengyu.system.service.ISysConfigService;
import com.shengyu.system.service.ISysDictTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * @description: 初始化redis缓存 <br>
 * @date: 2021/7/6 18:02 <br>
 * @author: lwy <br>
 * @version: 1.0 <br>
 */
@Component

public class InitializeRedisCache {
    @Resource
    private ISysConfigService configService;
    @Autowired
    private ISysAreaService areaService;
    @Autowired
    private CapitalConfig capitalConfig;
    @Autowired
    private ISysDictTypeService dictTypeService;
    @Autowired
    private ISysConfigService sysConfigService;

    /**
     * 项目启动时，初始化参数到缓存
     */
    @PostConstruct
    public void init() {
        // 初始化省市县/区数据到缓存
        areaService.initAreaToRedis();
        // 初始化字典数据到缓存
        dictTypeService.loadingDictCache();
        // 初始化配置数据到缓存
        sysConfigService.loadingConfigCache();

    }

}
