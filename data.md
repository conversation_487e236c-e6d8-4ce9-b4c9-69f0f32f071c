# 多租户数据隔离与边界设计（Custody 模块为例）

本文档说明项目在 Custody（托管）相关模块中的多租户数据隔离策略、边界与读写路径设计，并给出与代码的精确映射与验证清单。

## 1. 背景与目标
- 后台管理按租户隔离：同一套系统服务多个租户，后台管理端的所有读写默认受 TenantInterceptor 约束，严格限定到当前租户上下文，保证不同租户间数据彼此不可见。
- 服务订单按“服务的租户”归属：创建/更新订单时，从 CustodyServe 继承并显式写入 tenant_id，确保订单数据与服务的租户保持一致，为后续按租户进行统计、结算与审计提供可靠依据。
- App 端服务发现不使用租户过滤：面向用户的服务查询默认不以 tenant_id 作为过滤条件，以便用户在不同区域都能发现可用服务；涉及写入（如下单）时，订单仍以所选服务的租户确定归属并被准确记录。
- 目标：实现“后台严格隔离、App 广域发现、订单准确归属”的统一策略，在保证数据一致性与可审计性的同时，兼顾用户侧的服务可达性。

## 2. 架构总览（多租户边界）
```mermaid
flowchart LR
  subgraph 应用层
    Controller --> Service
  end

  subgraph 数据访问层
    Service -->|常规| Mapper[MyBatis-Plus Mapper]
    Service -->|@InterceptorIgnore| MapperIgnore[忽略租户的 Mapper]
  end

  subgraph DB[数据库]
    TB[(t_business_custody_book)]
    TS[(t_business_custody_serve)]
    TI[(t_business_custody_item)]
  end

  Mapper -->|自动SQL + TenantInterceptor| DB
  MapperIgnore -->|自定义XML + ignore| DB

  note1[[TenantInterceptor 为常规SQL自动追加 WHERE tenant_id = 当前租户]]
  note2[[忽略租户方法完全跳过TenantInterceptor，由Service显式设置并写入 tenant_id]]

  Mapper --- note1
  MapperIgnore --- note2
```

## 3. 写入与更新流程（App 客户端，显式保留 tenant_id）
```mermaid
sequenceDiagram
  participant C as App客户端
  participant S as CustodyBookServiceImpl
  participant M as BusinessCustodyBookMapper
  participant X as CustodyBookMapper.xml
  participant DB as 数据库

  C->>S: saveCustodyBook(CustodyBook)
  S->>S: selectByIdIgnoreTenant(custodyId) 从 Serve 读取
  S->>S: custodyBook.tenantId = serve.tenantId
  S->>S: fillDataFromServe() & validateTotalPrice()
  S->>M: insertWithServeTenantId(custodyBook) [@InterceptorIgnore]
  M->>X: <insert id="insertWithServeTenantId">
  X->>DB: INSERT ... (tenant_id 来自实体) // 不加租户过滤
  DB-->>S: OK

  C->>S: updateByIdWithUserCheck(CustodyBook, userId)
  S->>S: selectByIdUserId 校验归属与权限
  S->>S: 若 custodyId 变更 -> 重新设置 tenantId = serve.tenantId
  S->>M: updateByIdWithServeTenantId(custodyBook) [@InterceptorIgnore]
  M->>X: <update id="updateByIdWithServeTenantId">
  X->>DB: UPDATE ... SET tenant_id=? WHERE id=? // 不加租户过滤
  DB-->>S: OK
```

## 4. 读取路径（租户感知 vs 忽略租户）
```mermaid
flowchart TB
  A[Service Read] -->|Manage| B[selectPage_or_selectById]
  A -->|CrossTenant| C[selectPageIgnoreTenant_or_selectByIdIgnoreTenant]

  B -->|MP auto SQL| D[Normal Mapper]
  C -->|InterceptorIgnore + XML| E[Ignore-Tenant Mapper]

  D -->|TenantInterceptor adds WHERE tenant_id=ctx| DB[(DB)]
  E -->|No tenant filter| DB