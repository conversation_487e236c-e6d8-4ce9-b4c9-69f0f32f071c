package com.shengyu.web.websocket;

import com.alibaba.fastjson.JSONObject;
import com.shengyu.common.config.RedisClient;
import com.shengyu.common.constant.WebsocketConst;
import com.shengyu.common.core.domain.SocketMsg;
import com.shengyu.common.core.redis.RedisCache;
import com.shengyu.common.utils.JsonUtils;
import com.shengyu.common.utils.StringUtils;
import com.shengyu.common.utils.spring.SpringUtils;
import com.shengyu.framework.manager.AsyncManager;
import com.shengyu.framework.manager.factory.AsyncFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArraySet;

/**
 * <AUTHOR>
 * @Date 2019/11/29 9:41
 * @Description: 此注解相当于设置访问URL
 */
@Component
@Slf4j
@ServerEndpoint("/websocket/{userId}") //此注解相当于设置访问URL
public class WebSocket {

    private Session session;

    private String userId;

    @Resource
    private RedisClient redisClient;

    /**
     * 缓存 webSocket连接到单机服务class中（整体方案支持集群）
     */
    private static final CopyOnWriteArraySet<WebSocket> webSockets = new CopyOnWriteArraySet<>();
    private static final Map<String, Session> sessionPool = new HashMap<String, Session>();


    @OnOpen
    public void onOpen(Session session, @PathParam(value = "userId") String userId) {
        try {
            this.session = session;
            this.userId = userId;
            if (sessionPool.get(userId) != null) {
                commonValidMsg(userId);
                return;
            }
            webSockets.add(this);
            sessionPool.put(userId, session);
            commonValidMsg(userId);
            Map<String, Object> maps = null;
            SocketMsg sm = new SocketMsg("login", userId, maps);
            AsyncManager.me().execute(AsyncFactory.sendMsg(sm));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void commonValidMsg(String userId) {
        RedisCache redisCache = SpringUtils.getBean(RedisCache.class);
        Collection<String> keys = redisCache.keys(userId + ":exportMsg*");
        if (StringUtils.isNotEmpty(keys)) {
            keys.forEach(key -> {
                SocketMsg msg = redisCache.getCacheObject(key);
                pushMessage(msg);
                redisCache.deleteObject(key);
            });
        }
    }

    @OnClose
    public void onClose() {
        try {
            webSockets.remove(this);
            sessionPool.remove(this.userId);
            log.info("【websocket消息】连接断开，总数为:" + webSockets.size());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 服务端推送消息
     */
    synchronized
    public void pushMessage(SocketMsg msg) {
        Session session = sessionPool.get(msg.getUserId());
        if (session != null && session.isOpen()) {
            log.info("用户"+msg.getUserId()+"开始发送信息。。。");
            try {
                session.getAsyncRemote().sendText(JsonUtils.getJsonString(msg));
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            if ("exportInfo".equals(msg.getType())) {
                RedisCache redisCache = SpringUtils.getBean(RedisCache.class);
                redisCache.setCacheObject(msg.getUserId() + ":exportMsg" + StringUtils.randomStr(6), msg);
            }
        }
    }

    /**
     * 服务器端推送消息
     */
    synchronized
    public void pushMessage(String message) {
        try {
            webSockets.forEach(ws -> ws.session.getAsyncRemote().sendText(message));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @OnMessage
    public void onMessage(String message, Session session) {
        log.debug("【websocket消息】收到客户端消息:" + message);
        JSONObject obj = new JSONObject();
        //业务类型
        obj.put(WebsocketConst.MSG_CMD, WebsocketConst.CMD_CHECK);
        //消息内容
        obj.put(WebsocketConst.MSG_TXT, "心跳响应");
        SocketMsg sm = new SocketMsg(WebsocketConst.CMD_CHECK, null, obj);
        session.getAsyncRemote().sendText(JsonUtils.getJsonString(sm));
    }

    /**
     * 后台发送消息到redis
     *
     * @param message
     */
    public void sendMessage(String message) {
        log.info("【websocket消息】广播消息:" + message);
        Map<String, Object> data = new HashMap<>();
        data.put("msgText", message);
        SocketMsg sm = new SocketMsg("normal", "", data);
        redisClient.sendMessage(sm);
    }

    /**
     * 此为单点消息
     *
     * @param userId
     * @param message
     */
    public void sendMessage(String userId, String message) {
        SocketMsg sm = new SocketMsg("normal", userId, null);
        redisClient.sendMessage(sm);
    }

    /**
     * 此为单点消息(多人)
     *
     * @param userIds
     * @param message
     */
    public void sendMessage(String[] userIds, String message) {
        for (String userId : userIds) {
            sendMessage(userId, message);
        }
    }

    @OnError
    public void onError(Session session, Throwable e) {
        if (e instanceof java.io.EOFException) {
            log.error("【websocket消息】连接被客户端异常关闭: {}", session != null ? session.getId() : "未知session");
        } else {
            log.error("【websocket消息】出现未知错误 ", e);
        }
    }

}