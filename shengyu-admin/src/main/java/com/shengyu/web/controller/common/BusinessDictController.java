package com.shengyu.web.controller.common;

import com.shengyu.common.core.controller.BaseController;
import com.shengyu.common.core.domain.AjaxResult;
import com.shengyu.common.core.domain.entity.SysDictData;
import com.shengyu.system.service.ISysDictDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "业务字典")
@RequestMapping("/business/dict")
@RestController
public class BusinessDictController extends BaseController {

    @Resource
    private ISysDictDataService sysDictDataService;

    /**
     * 业务字典--作物类型
     */
    @ApiOperation(value = "农事服务--作物类型")
    @PostMapping("/cropType")
    public AjaxResult<List<SysDictData>> cropType() {
        return AjaxResult.success(sysDictDataService.selectDictDataList(
                SysDictData.builder().dictType("business_crop_type").build()));
    }

    /**
     * 业务字典--托管服务
     */
    @ApiOperation(value = "托管服务--服务类型")
    @PostMapping("/custodyServeType")
    public AjaxResult<List<SysDictData>> custodyServeType() {
        return AjaxResult.success(sysDictDataService.selectDictDataList(
                SysDictData.builder().dictType("business_custody_type").build()));
    }

    /**
     * 业务字典--农机服务
     */
    @ApiOperation(value = "农机服务--服务类型")
    @PostMapping("/machineServeType")
    public AjaxResult<List<SysDictData>> machineServeType() {
        return AjaxResult.success(sysDictDataService.selectDictDataList(
                SysDictData.builder().dictType("business_machine_serve_type").build()));
    }

    /**
     * 业务字典--飞防服务
     */
    @ApiOperation(value = "飞防服务--服务类型")
    @PostMapping("/planServeType")
    public AjaxResult<List<SysDictData>> planServeType() {
        return AjaxResult.success(sysDictDataService.selectDictDataList(
                SysDictData.builder().dictType("business_plane_serve_type").build()));
    }

    /**
     * 业务字典--供需市场
     */
    @ApiOperation(value = "供需市场--服务类型")
    @PostMapping("/supplyDemandType")
    public AjaxResult<List<SysDictData>> supplyDemandType() {
        return AjaxResult.success(sysDictDataService.selectDictDataList(
                SysDictData.builder().dictType("business_supply_demand_type").build()));
    }

    /**
     * 业务字典--供需市场服务类型
     */
    @ApiOperation(value = "供需市场--供需服务类型")
    @PostMapping("/supplyDemandServeType")
    public AjaxResult<List<SysDictData>> supplyDemandServeType() {
        return AjaxResult.success(sysDictDataService.selectDictDataList(
                SysDictData.builder().dictType("business_supply_demand_serve_type").build()));
    }

    /**
     * 业务字典--预定状态
     */
    @ApiOperation(value = "农事服务--预定状态")
    @PostMapping("/bookStatus")
    public AjaxResult<List<SysDictData>> bookStatus() {
        return AjaxResult.success(sysDictDataService.selectDictDataList(
                SysDictData.builder().dictType("business_book_status").build()));
    }

    /**
     * 业务字典--审核状态
     */
    @ApiOperation(value = "农事服务--审核状态")
    @PostMapping("/auditStatus")
    public AjaxResult<List<SysDictData>> auditStatus() {
        return AjaxResult.success(sysDictDataService.selectDictDataList(
                SysDictData.builder().dictType("business_audit_status").build()));
    }

    /**
     * 业务字典--上架下架状态
     */
    @ApiOperation(value = "农事服务--上架下架状态")
    @PostMapping("/shelfStatus")
    public AjaxResult<List<SysDictData>> shelfStatus() {
        return AjaxResult.success(sysDictDataService.selectDictDataList(
                SysDictData.builder().dictType("business_shelf_status").build()));
    }

    /**
     * 商城-服务商类型
     */
    @ApiOperation(value = "商城--服务商类型")
    @PostMapping("/providerType")
    public AjaxResult<List<SysDictData>> serviceProviderType() {
        return AjaxResult.success(sysDictDataService.selectDictDataList(
                SysDictData.builder().dictType("mall_service_provider_type").build()));
    }

    /**
     * 商城--服务类别
     */
    @ApiOperation(value = "商城--服务类别")
    @PostMapping("/serveType")
    public AjaxResult<List<SysDictData>> serveType() {
        return AjaxResult.success(sysDictDataService.selectDictDataList(
                SysDictData.builder().dictType("mall_service_category").build()));
    }

    /**
     * 商城--服务商等级
     */
    @ApiOperation(value = "商城--服务商等级")
    @PostMapping("/providerLevel")
    public AjaxResult<List<SysDictData>> providerLevel() {
        return AjaxResult.success(sysDictDataService.selectDictDataList(
                SysDictData.builder().dictType("mall_service_provider_level").build()));
    }

}
