package com.shengyu.web.controller.business;

import com.shengyu.business.domain.MachineBook;
import com.shengyu.business.domain.dto.MachineBookDto;
import com.shengyu.business.service.IMachineBookService;
import com.shengyu.common.annotation.Log;
import com.shengyu.common.core.controller.BaseController;
import com.shengyu.common.core.domain.AjaxResult;
import com.shengyu.common.core.page.TableDataInfo;
import com.shengyu.common.enums.BusinessType;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 农机服务预定
 */
@Api(tags = "农机服务预定")
@RestController
@RequestMapping("/machine/book")
public class BusinessMachineBookController extends BaseController {

    @Resource
    private IMachineBookService machineBookService;

    /**
     * 农机服务预定
     */
    @ApiOperation(value = "农机服务预定--列表")
    @PreAuthorize("@ss.hasPermi('machine:book:list')")
    @PostMapping("/list")
    public TableDataInfo<MachineBookDto> list(MachineBook machineBook) {
        Page<MachineBookDto> page = buildPage();
        return getDataTable(machineBookService.selectPage(page, machineBook));
    }

    /**
     * 农机服务预定--详情
     */
    @ApiOperation(value = "农机服务预定--详情")
    @PreAuthorize("@ss.hasPermi('machine:book:query')")
    @PostMapping("/query")
    public AjaxResult<MachineBook> query(@RequestParam Long id) {
        return success(machineBookService.selectById(id));
    }

    /**
     * 农机服务预定--处理
     */
    @ApiOperation(value = "农机服务预定--处理")
    @PreAuthorize("@ss.hasPermi('machine:book:deal')")
    @Log(title = "农机服务预定--处理", businessType = BusinessType.UPDATE)
    @PostMapping("/deal")
    public AjaxResult<String> deal(@RequestBody MachineBook machineBook) {
        return toAjax(machineBookService.updateById(machineBook));
    }
}
