package com.shengyu.web.controller.common;

import com.shengyu.common.config.CapitalConfig;
import com.shengyu.common.core.controller.BaseController;
import com.shengyu.common.core.domain.MapResult;
import com.shengyu.common.utils.StringUtils;
import com.shengyu.common.utils.file.ObsUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@Api(tags = "图片上传接口")
@RestController
public class ImageController extends BaseController {
    /** 允许的最大图片大小：5MB */
    private static final long MAX_IMAGE_SIZE = 5 * 1024 * 1024;

    /**
     * 上传banner图片
     * 支持单个图片文件上传
     *
     * @param file 上传的图片文件
     * @return 包含图片URL的MapResult对象
     */
//    @Log(title = "上传banner", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "上传banner")
    @PostMapping("/banner")
    public MapResult uploadBanner(@RequestParam("file") MultipartFile file) {
        try {
            String url = uploadOne(file, CapitalConfig.getBusinessPath());
            MapResult result = MapResult.success();
            result.put("imgUrl", url);
            return result;
        } catch (IOException e) {
            logger.error("Banner图片上传IO异常: {}", e.getMessage(), e);
            return MapResult.error("图片上传失败，请稍后重试");
        } catch (IllegalArgumentException e) {
            return MapResult.error(e.getMessage());
        } catch (Exception e) {
            logger.error("Banner图片上传失败: {}", e.getMessage(), e);
            return MapResult.error("图片上传失败，请稍后重试");
        }
    }

    @ApiOperation(value = "批量上传供需服务图片")
    @PostMapping("/uploadSupplyDemand/batch")
    public MapResult uploadSupplyDemandBatch(@RequestParam("files") List<MultipartFile> files) {
        try {
            List<String> urls = uploadMany(files, CapitalConfig.getSupplyDemandPath());
            MapResult result = MapResult.success();
            result.put("imgUrls", urls);
            return result;
        } catch (IOException e) {
            logger.error("批量供需服务图片上传IO异常: {}", e.getMessage(), e);
            return MapResult.error("图片上传失败，请稍后重试");
        } catch (IllegalArgumentException e) {
            return MapResult.error(e.getMessage());
        } catch (Exception e) {
            logger.error("批量供需服务图片上传失败: {}", e.getMessage(), e);
            return MapResult.error("图片上传失败，请稍后重试");
        }
    }

    /**
     * 校验图片文件扩展名
     *
     * @param filename 文件名
     * @return 是否有效
     */
    private boolean validateImageFile(String filename) {
        if (StringUtils.isEmpty(filename)) {
            return false;
        }

        String lowerName = filename.toLowerCase();
        return lowerName.endsWith(".jpg") ||
                lowerName.endsWith(".jpeg") ||
                lowerName.endsWith(".png") ||
                lowerName.endsWith(".gif") ||
                lowerName.endsWith(".webp");
    }

    /**
     * 通用：上传单个图片（含校验）
     */
    private String uploadOne(MultipartFile file, String basePath) throws IOException {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("请选择要上传的图片文件");
        }

        String filename = file.getOriginalFilename();
        if (!validateImageFile(filename)) {
            throw new IllegalArgumentException("仅支持JPG/PNG/GIF/WEBP格式的图片");
        }

        if (file.getSize() > MAX_IMAGE_SIZE) {
            throw new IllegalArgumentException("图片大小不能超过5MB");
        }

        String filePath = ObsUtil.upload(file, basePath);
        return ObsUtil.viewPublicFile(filePath);
    }

    /**
     * 通用：批量上传图片（含校验，返回URL列表）
     */
    private List<String> uploadMany(List<MultipartFile> files, String basePath) throws IOException {
        if (files == null || files.isEmpty()) {
            throw new IllegalArgumentException("请至少选择一张图片");
        }
        List<String> urls = new ArrayList<>(files.size());
        for (MultipartFile f : files) {
            if (f == null || f.isEmpty()) {
                continue; // 跳过空文件位
            }
            urls.add(uploadOne(f, basePath));
        }
        if (urls.isEmpty()) {
            throw new IllegalArgumentException("未检测到有效图片文件");
        }
        return urls;
    }
}
