package com.shengyu.web.controller.business;


import com.shengyu.business.domain.SupplyDemand;
import com.shengyu.business.domain.dto.SupplyDemandDto;
import com.shengyu.business.service.ISupplyDemandService;
import com.shengyu.common.annotation.Log;
import com.shengyu.common.core.controller.BaseController;
import com.shengyu.common.core.domain.AjaxResult;
import com.shengyu.common.core.page.TableDataInfo;
import com.shengyu.common.enums.BusinessType;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Api(tags = "供需信息")
@RestController
@RequestMapping("/business/supplyDemand")
public class BusinessSupplyDemandController extends BaseController {

    @Resource
    private ISupplyDemandService supplyDemandService;

    @ApiOperation(value = "供需信息--列表")
    @PreAuthorize("@ss.hasPermi('supplyDemand:manage:list')")
    @PostMapping("/list")
    public TableDataInfo<SupplyDemandDto> list(SupplyDemandDto supplyDemandDto) {
        Page<SupplyDemandDto> page = buildPage();
        return getDataTable(supplyDemandService.selectPage(page, supplyDemandDto));
    }

    @ApiOperation(value = "供需信息--详情")
    @PreAuthorize("@ss.hasPermi('supplyDemand:manage:query')")
    @PostMapping("/query")
    public AjaxResult<SupplyDemand> query(@RequestParam Long id) {
        return success(supplyDemandService.selectOne(id));
    }

    @ApiOperation(value = "供需信息--审核")
    @PreAuthorize("@ss.hasPermi('supplyDemand:manage:audit')")
    @Log(title = "供需信息-审核", businessType = BusinessType.UPDATE)
    @PostMapping("/audit")
    public AjaxResult<String> audit(@RequestBody SupplyDemand supplyDemand) {
        return toAjax(supplyDemandService.updateById(supplyDemand));
    }
}
