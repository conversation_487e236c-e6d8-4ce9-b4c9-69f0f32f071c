package com.shengyu.app.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.shengyu.business.domain.PlaneBook;
import com.shengyu.business.domain.PlaneServe;
import com.shengyu.business.domain.dto.PlaneBookDto;
import com.shengyu.business.service.IPlaneBookService;
import com.shengyu.business.service.IPlaneServeService;
import com.shengyu.common.core.domain.AjaxResult;
import com.shengyu.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

@Api(tags = "app--飞防服务")
@RestController
@RequestMapping("/app/planServe")
public class PlanServeController extends BaseAppController {

    @Autowired
    private IPlaneServeService planServeService;

    @Autowired
    private IPlaneBookService planeBookService;

    // ====================================================================================
    // ============================== 飞防服务 (PlaneServe) ==============================
    // ====================================================================================

    @ApiOperation(value = "飞防服务--详情")
    @PostMapping("/serve/query")
    public AjaxResult<PlaneServe> queryServe(@RequestParam String serveType) {
        PlaneServe result = planServeService.selectByServeTypeIgnoreTenant(serveType);
        if (Objects.isNull(result)) {
            return AjaxResult.error("未找到对应服务类型的数据");
        }
        return AjaxResult.success(result);
    }

    // ====================================================================================
    // ============================== 飞防预定 (PlaneBook) ===============================
    // ====================================================================================

    @ApiOperation(value = "飞防服务预定--列表")
    @PostMapping("/book/list")
    public TableDataInfo<PlaneBookDto> bookList(PlaneBook planeBook) {
        planeBook.setBookUserId(getLoginUser().getUserId());
        Page<PlaneBookDto> page = buildPage();
        return getDataTable(planeBookService.selectPageIgnoreTenant(page, planeBook));
    }

    @ApiOperation(value = "飞防服务预定--详情")
    @PostMapping("/book/query")
    public AjaxResult<PlaneBookDto> bookQuery(@RequestParam Long id) {
        return AjaxResult.success(planeBookService.selectByIdUserIdIgnoreTenant(id, getLoginUser().getUserId()));
    }

    @ApiOperation(value = "飞防服务预定--新增")
    @PostMapping("/book/save")
    public AjaxResult<Boolean> bookSave(@RequestBody PlaneBook planeBook) {
        try {
            planeBook.setBookUserId(getLoginUser().getUserId());
            return AjaxResult.success(planeBookService.savePlaneBookIgnoreTenant(planeBook));
        } catch (Exception e) {
            logger.error("新增飞防服务预定失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    @ApiOperation(value = "飞防服务预定--修改")
    @PostMapping("/book/edit")
    public AjaxResult<Boolean> bookUpdate(@RequestBody PlaneBook planeBook) {
        try {
            Long loginUserId = getLoginUser().getUserId();
            return AjaxResult.success(planeBookService.updateByIdIgnoreTenant(planeBook, loginUserId));
        } catch (Exception e) {
            logger.error("修改飞防服务预定失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    @ApiOperation(value = "飞防服务预定--删除")
    @DeleteMapping("/book/delete")
    public AjaxResult<Boolean> bookDelete(@RequestParam Long id) {
        try {
            Long loginUserId = getLoginUser().getUserId();
            return AjaxResult.success(planeBookService.removeByIdIgnoreTenant(id, loginUserId));
        } catch (Exception e) {
            logger.error("删除飞防服务预定失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

}
