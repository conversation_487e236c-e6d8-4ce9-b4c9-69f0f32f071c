package com.shengyu.app.controller.dto;

import java.io.Serializable;
import java.util.List;

import com.shengyu.common.core.domain.entity.SysUser;
import lombok.Data;

@Data
public class AppUserDto extends SysUser implements Serializable {

    private List<String> permissions;

//    @Data
//    public static class UserBean implements Serializable {
//        /**
//         * searchValue :
//         * createBy : nzs123.
//         * createTime : 2023-06-06 14:54:14
//         * updateBy :
//         * updateTime : null
//         * remark : 报录村经办人发起申请，完成支付
//         * params : {}
//         * userId : 8
//         * deptId : 10012
//         * userName : baolu001
//         * nickName : 报录村经办人
//         * wechatName :
//         * openId :
//         * email :
//         * phonenumber :
//         * sex : 0
//         * avatar :
//         * salt :
//         * status : 1
//         * delFlag : 0
//         * loginIp : *************
//         * loginDate : 2023-06-09T09:23:23.926+08:00
//         * dept : {"searchValue":"","createBy":"","createTime":null,"updateBy":"","updateTime":null,"remark":"","params":{},"deptId":"10012","parentId":"10011","ancestors":"0,10010,10011","deptName":"报录村","deptCode":"blc","orderNum":"0","leader":"","phone":"","email":"","status":"1","delFlag":"","parentName":"","address":"","areaId":"1627","merName":"","level":3,"mapCapitalId":"","tenantId":"","children":[]}
//         * roles : [{"searchValue":"","createBy":"","createTime":null,"updateBy":"","updateTime":null,"remark":"","params":{},"roleId":"16","roleName":"村经办人","roleKey":"villageCreate","roleSort":"0","dataScope":"1","menuCheckStrictly":false,"deptCheckStrictly":false,"status":"1","delFlag":"","flag":false,"menuIds":null,"deptIds":null,"tenantId":"","admin":false}]
//         * roleIds : null
//         * postIds : null
//         * notInRoomId : 0
//         * notInLabId : 0
//         * loginType : 0
//         * merName : 河南省-濮阳市-清丰县
//         * userType : 00
//         * areaId : 1627
//         * roleNameStr :
//         * stuId : 0
//         * tenantId : 6
//         * admin : false
//         */
//
//        private String searchValue;
//        private String createBy;
//        private String createTime;
//        private String updateBy;
//        private String idCard;
//        private Object updateTime;
//        private String remark;
//        private ParamsBean params;
//        private String userId;
//        private String deptId;
//        private String userName;
//        private String nickName;
//        private String wechatName;
//        private String openId;
//        private String email;
//        private String phonenumber;
//        private String sex;
//        private String avatar;
//        private String salt;
//        private String status;
//        private String delFlag;
//        private String loginIp;
//        private String loginDate;
//        private DeptBean dept;
//        private Object roleIds;
//        private Object postIds;
//        private String notInRoomId;
//        private String notInLabId;
//        private int loginType;
//        private String merName;
//        private String userType;
//        private String areaId;
//        private String roleNameStr;
//        private String stuId;
//        private String tenantId;
//        private boolean admin;
//        private List<RolesBean> roles;
//
//        @Data
//        public static class ParamsBean implements Serializable {
//        }
//
//        @Data
//        public static class DeptBean implements Serializable {
//            /**
//             * searchValue :
//             * createBy :
//             * createTime : null
//             * updateBy :
//             * updateTime : null
//             * remark :
//             * params : {}
//             * deptId : 10012
//             * parentId : 10011
//             * ancestors : 0,10010,10011
//             * deptName : 报录村
//             * deptCode : blc
//             * orderNum : 0
//             * leader :
//             * phone :
//             * email :
//             * status : 1
//             * delFlag :
//             * parentName :
//             * address :
//             * areaId : 1627
//             * merName :
//             * level : 3
//             * mapCapitalId :
//             * tenantId :
//             * children : []
//             */
//
//            private String searchValue;
//            private String createBy;
//            private Object createTime;
//            private String updateBy;
//            private Object updateTime;
//            private String remark;
//            private ParamsBeanX params;
//            private String deptId;
//            private String parentId;
//            private String ancestors;
//            private String deptName;
//            private String deptCode;
//            private String orderNum;
//            private String leader;
//            private String phone;
//            private String email;
//            private String status;
//            private String delFlag;
//            private String parentName;
//            private String address;
//            private String areaId;
//            private String merName;
//            private int level;
//            private String mapCapitalId;
//            private String tenantId;
//            private List<?> children;
//
//            @Data
//            public static class ParamsBeanX implements Serializable {
//            }
//        }
//
//        @Data
//        public static class RolesBean implements Serializable {
//            /**
//             * searchValue :
//             * createBy :
//             * createTime : null
//             * updateBy :
//             * updateTime : null
//             * remark :
//             * params : {}
//             * roleId : 16
//             * roleName : 村经办人
//             * roleKey : villageCreate
//             * roleSort : 0
//             * dataScope : 1
//             * menuCheckStrictly : false
//             * deptCheckStrictly : false
//             * status : 1
//             * delFlag :
//             * flag : false
//             * menuIds : null
//             * deptIds : null
//             * tenantId :
//             * admin : false
//             */
//
//            private String searchValue;
//            private String createBy;
//            private Object createTime;
//            private String updateBy;
//            private Object updateTime;
//            private String remark;
//            private ParamsBeanXX params;
//            private String roleId;
//            private String roleName;
//            private String roleKey;
//            private String roleSort;
//            private String dataScope;
//            private boolean menuCheckStrictly;
//            private boolean deptCheckStrictly;
//            private String status;
//            private String delFlag;
//            private boolean flag;
//            private Object menuIds;
//            private Object deptIds;
//            private String tenantId;
//            private boolean admin;
//
//            @Data
//            public static class ParamsBeanXX implements Serializable {
//            }
//        }
//    }
}
