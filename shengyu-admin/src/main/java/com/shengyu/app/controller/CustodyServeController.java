package com.shengyu.app.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.shengyu.business.domain.CustodyBook;
import com.shengyu.business.domain.dto.CustodyBookDto;
import com.shengyu.business.domain.dto.CustodyServeDto;
import com.shengyu.business.service.ICustodyBookService;
import com.shengyu.business.service.ICustodyServeService;
import com.shengyu.common.core.domain.AjaxResult;
import com.shengyu.common.core.page.TableDataInfo;
import com.shengyu.common.exception.CustomException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(tags = "app--托管服务")
@RestController
@RequestMapping("/app/custody")
public class CustodyServeController extends BaseAppController {

    @Autowired
    private ICustodyServeService custodyServeService;

    @Autowired
    private ICustodyBookService custodyBookService;

    // ====================================================================================
    // =============================== 托管服务 (Serve) ===================================
    // ====================================================================================

    @ApiOperation(value = "托管服务--列表")
    @PostMapping("/serve/list")
    public TableDataInfo<CustodyServeDto> serveList(CustodyServeDto custodyServeDto) {
        custodyServeDto.setStatus(1);
        Page<CustodyServeDto> page = buildPage();
        return getDataTable(custodyServeService.selectPageIgnoreTenant(page, custodyServeDto));
    }

    @ApiOperation(value = "托管服务--详情")
    @PostMapping("/serve/query")
    public AjaxResult<CustodyServeDto> serveQuery(@RequestParam Long id) {
        CustodyServeDto dto = custodyServeService.selectByIdIgnoreTenant(id);
        return AjaxResult.success(dto);
    }

    // ====================================================================================
    // =============================== 托管预定 (Book) ====================================
    // ====================================================================================

    @ApiOperation(value = "托管预定--列表")
    @PostMapping("/book/list")
    public TableDataInfo<CustodyBookDto> bookList(CustodyBookDto custodyBookDto) {
        custodyBookDto.setBookUserId(getLoginUser().getUserId());
        Page<CustodyBookDto> page = buildPage();
        return getDataTable(custodyBookService.selectPageIgnoreTenant(page, custodyBookDto));
    }

    @ApiOperation(value = "托管预定--详情")
    @PostMapping("/book/query")
    public AjaxResult<CustodyBookDto> bookQuery(@RequestParam Long id) {
        CustodyBookDto custodyBookDto = custodyBookService.selectByIdUserIdIgnoreTenant(id, getLoginUser().getUserId());
        return AjaxResult.success(custodyBookDto);
    }

    @ApiOperation(value = "托管预定--新增")
    @PostMapping("/book/save")
    public AjaxResult<Boolean> bookSave(@RequestBody CustodyBook custodyBook) {
        try {
            custodyBook.setBookUserId(getLoginUser().getUserId());
            return AjaxResult.success(custodyBookService.saveCustodyBookIgnoreTenant(custodyBook));
        } catch (Exception e) {
            logger.error("新增托管预定失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    @ApiOperation(value = "托管预定--修改")
    @PostMapping("/book/edit")
    public AjaxResult<Boolean> bookUpdate(@RequestBody CustodyBook custodyBook) {
        try {
            Long loginUserId = getLoginUser().getUserId();
            return AjaxResult.success(custodyBookService.updateByIdIgnoreTenant(custodyBook, loginUserId));
        } catch (CustomException e) {
            logger.error("修改托管预定失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    @ApiOperation(value = "托管预定--删除")
    @DeleteMapping("/book/delete")
    public AjaxResult<Boolean> bookDelete(@RequestParam Long id) {
        try {
            Long loginUserId = getLoginUser().getUserId();
            return AjaxResult.success(custodyBookService.removeByIdIgnoreTenant(id, loginUserId));
        } catch (CustomException e) {
            logger.error("删除托管预定失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

}
