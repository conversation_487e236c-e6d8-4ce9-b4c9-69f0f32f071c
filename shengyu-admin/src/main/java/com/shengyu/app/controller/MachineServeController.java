package com.shengyu.app.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.shengyu.business.domain.MachineBook;
import com.shengyu.business.domain.MachineServe;
import com.shengyu.business.domain.dto.MachineBookDto;
import com.shengyu.business.service.IMachineBookService;
import com.shengyu.business.service.IMachineServeService;
import com.shengyu.common.core.domain.AjaxResult;
import com.shengyu.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(tags = "app--农机服务")
@RestController
@RequestMapping("/app/machineServe")
public class MachineServeController extends BaseAppController {

    @Autowired
    private IMachineServeService machineServeService;

    @Autowired
    private IMachineBookService machineBookService;

    @ApiOperation(value = "农机服务--详情")
    @PostMapping("/serve/query")
    public AjaxResult<MachineServe> queryServe(@RequestParam String serveType) {
        MachineServe result = machineServeService.selectByServeTypeIgnoreTenant(serveType);
        if (result == null) {
            return AjaxResult.error("未找到对应服务类型的数据");
        }
        return AjaxResult.success(result);
    }

    @ApiOperation(value = "农机服务预定--列表")
    @PostMapping("/book/list")
    public TableDataInfo<MachineBookDto> bookList(MachineBook machineBook) {
        machineBook.setBookUserId(getLoginUser().getUserId());
        Page<MachineBookDto> page = buildPage();
        return getDataTable(machineBookService.selectPageIgnoreTenant(page, machineBook));
    }

    @ApiOperation(value = "农机服务预定--详情")
    @PostMapping("/book/query")
    public AjaxResult<MachineBookDto> bookQuery(@RequestParam Long id) {
        return AjaxResult.success(machineBookService.selectByIdUserIdIgnoreTenant(id, getLoginUser().getUserId()));
    }

    @ApiOperation(value = "农机服务预定--新增")
    @PostMapping("/book/save")
    public AjaxResult<Boolean> bookSave(@RequestBody MachineBook machineBook) {
        try {
            machineBook.setBookUserId(getLoginUser().getUserId());
            return AjaxResult.success(machineBookService.saveMachineBookIgnoreTenant(machineBook));
        } catch (Exception e) {
            logger.error("新增农机服务预定失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    @ApiOperation(value = "农机服务预定--修改")
    @PostMapping("/book/edit")
    public AjaxResult<Boolean> bookUpdate(@RequestBody MachineBook machineBook) {
        try {
            Long loginUserId = getLoginUser().getUserId();
            return AjaxResult.success(machineBookService.updateByIdIgnoreTenant(machineBook, loginUserId));
        } catch (Exception e) {
            logger.error("修改农机服务预定失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    @ApiOperation(value = "农机服务预定--删除")
    @DeleteMapping("/book/delete")
    public AjaxResult<Boolean> bookDelete(@RequestParam Long id) {
        try {
            Long loginUserId = getLoginUser().getUserId();
            return AjaxResult.success(machineBookService.removeByIdIgnoreTenant(id, loginUserId));
        } catch (Exception e) {
            logger.error("删除农机服务预定失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

}
