package com.shengyu.app.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.shengyu.business.domain.SupplyDemand;
import com.shengyu.business.domain.dto.SupplyDemandDto;
import com.shengyu.business.enums.AuditStatusEnum;
import com.shengyu.business.service.ISupplyDemandService;
import com.shengyu.common.core.domain.AjaxResult;
import com.shengyu.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(tags = "app--供需服务")
@RestController
@RequestMapping("/app/supplyDemandServe")
public class SupplyDemandServeController extends BaseAppController {

    @Autowired
    private ISupplyDemandService supplyDemandService;

    // ====================================================================================
    // ============================== 公共供需信息查询 ====================================
    // ====================================================================================

    @ApiOperation(value = "供需服务--列表")
    @PostMapping("/list")
    public TableDataInfo<SupplyDemandDto> list(SupplyDemandDto supplyDemandDto) {
        supplyDemandDto.setUserId(null);
        supplyDemandDto.setAuditStatus(AuditStatusEnum.PASS);
        Page<SupplyDemandDto> page = buildPage();
        IPage<SupplyDemandDto> result = supplyDemandService.selectPageIgnoreTenant(page, supplyDemandDto);
        return getDataTable(result);
    }

    @ApiOperation(value = "供需服务--详情")
    @PostMapping("/query")
    public AjaxResult<SupplyDemandDto> query(@RequestParam Long id) {
        return AjaxResult.success(supplyDemandService.selectValidByIdIgnoreTenant(id));
    }

    // ====================================================================================
    // ============================== 个人供需信息管理 ====================================
    // ====================================================================================

    @ApiOperation(value = "供需服务--我的供需列表")
    @PostMapping("/personal/list")
    public TableDataInfo<SupplyDemandDto> myList(SupplyDemandDto supplyDemandDto) {
        supplyDemandDto.setUserId(getLoginUser().getUserId());
        Page<SupplyDemandDto> page = buildPage();
        IPage<SupplyDemandDto> result = supplyDemandService.selectPageIgnoreTenant(page, supplyDemandDto);
        return getDataTable(result);
    }

    @ApiOperation(value = "供需服务--我的供需详情")
    @PostMapping("/personal/query")
    public AjaxResult<SupplyDemandDto> myQuery(@RequestParam Long id) {
        Long userId = getLoginUser().getUserId();
        return AjaxResult.success(supplyDemandService.selectByIdUserIdIgnoreTenant(id, userId));
    }

    @ApiOperation(value = "供需服务--发布")
    @PostMapping("/personal/publish")
    public AjaxResult<Boolean> publish(@RequestBody SupplyDemand supplyDemand) {
        try {
            Long loginUserId = getLoginUser().getUserId();
            supplyDemand.setUserId(loginUserId);
            return AjaxResult.success(supplyDemandService.saveSupplyDemandIgnoreTenant(supplyDemand));
        } catch (Exception e) {
            logger.error("新增供需服务失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    @ApiOperation(value = "供需服务--修改")
    @PostMapping("/personal/edit")
    public AjaxResult<Boolean> update(@RequestBody SupplyDemand supplyDemand) {
        try {
            Long loginUserId = getLoginUser().getUserId();
            return AjaxResult.success(supplyDemandService.updateByIdUserIdIgnoreTenant(supplyDemand, loginUserId));
        } catch (Exception e) {
            logger.error("修改供需服务失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    @ApiOperation(value = "供需服务--删除")
    @DeleteMapping("/personal/delete")
    public AjaxResult<Boolean> delete(@RequestParam Long id) {
        try {
            Long loginUserId = getLoginUser().getUserId();
            return AjaxResult.success(supplyDemandService.removeByIdUserIdIgnoreTenant(id, loginUserId));
        } catch (Exception e) {
            logger.error("删除供需服务失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }
}
