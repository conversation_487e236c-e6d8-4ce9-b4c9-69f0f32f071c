package com.shengyu.app.controller;

import com.shengyu.app.controller.dto.AppUserDto;
import com.shengyu.common.core.domain.AjaxResult;
import com.shengyu.common.core.domain.entity.SysUser;
import com.shengyu.common.utils.StringUtils;
import com.shengyu.system.service.ISysDeptService;
import com.shengyu.system.service.ISysRoleService;
import com.shengyu.system.service.ISysUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "app-用户服务")
@RestController
@RequestMapping("/app/user")
public class UserController extends BaseAppController {

    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysDeptService deptService;

    @Autowired
    private ISysRoleService roleService;

    @ApiOperation(value = "用户服务--获取当前用户详细信息")
    @PostMapping(value = "/getInfo")
    public AjaxResult<AppUserDto> getInfo() {
        Long userId = getLoginUser().getUserId();
        AppUserDto user = new AppUserDto();
        if (StringUtils.isNotNull(userId)) {
            SysUser sysUser = userService.selectUserById(userId);
            BeanUtils.copyProperties(sysUser, user);
            user.setDept(deptService.getDeptIdByAreaId(user.getAreaId()));
            user.setRoles(roleService.selectRolesByUserId(userId));
        }
        return success(user);
    }
}
