package com.shengyu.api.controller.common;

import com.alibaba.fastjson.JSONObject;
import com.shengyu.common.constant.Constants;
import com.shengyu.common.constant.KeysConstants;
import com.shengyu.common.constant.UserConstants;
import com.shengyu.common.core.domain.AjaxResult;
import com.shengyu.common.core.domain.dto.ForeignParam;
import com.shengyu.common.core.domain.entity.SysDept;
import com.shengyu.common.core.domain.entity.SysUser;
import com.shengyu.common.core.redis.RedisCache;
import com.shengyu.common.exception.CustomException;
import com.shengyu.common.utils.DateUtils;
import com.shengyu.common.utils.SecurityUtils;
import com.shengyu.common.utils.StringUtils;
import com.shengyu.common.utils.sign.SecurityDesUtil;
import com.shengyu.system.service.ISysDeptService;
import com.shengyu.system.service.ISysUserService;
import com.xxl.sso.core.login.SsoTokenLoginHelper;
import com.xxl.sso.core.user.XxlSsoUser;
import io.swagger.annotations.Api;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.util.encoders.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.nio.charset.StandardCharsets;

/**
 * @Author: SS
 * @Date: 2023/06/28/9:32
 * @Description:
 */
@Api(tags = "公共接口")
@RequestMapping("/api")
@RestController
@Slf4j
public class PublicController {

    @Autowired
    private RedisCache redisCache;
    @Autowired
    private ISysUserService userService;
    @Autowired
    private ISysDeptService deptService;

    @SneakyThrows
    @PostMapping("/syncUser")
    public AjaxResult<String> syncUser(@RequestBody ForeignParam param) {
        checkCanVisit(param);
        byte[] desData = Base64.decode(param.getData());
        byte[] desByte = SecurityDesUtil.tripleDESDecrypt(desData, KeysConstants.RSA_SECRET);
        SysUser user = JSONObject.parseObject(new String(desByte, StandardCharsets.UTF_8), SysUser.class);
        SysUser dataUser = userService.getUserByIdCard(user.getIdCard());
        int res;
        if (StringUtils.isNull(dataUser) && !"2".equals(user.getDelFlag())) {
            if (UserConstants.NOT_UNIQUE.equals(userService.checkUserNameUnique(user.getUserName()))) {
                return AjaxResult.error("新增用户'" + user.getUserName() + "'失败，登录账号已存在");
            } else if (StringUtils.isNotEmpty(user.getPhonenumber())
                    && UserConstants.NOT_UNIQUE.equals(userService.checkPhoneUnique(user))) {
                return AjaxResult.error("新增用户'" + user.getUserName() + "'失败，手机号码已存在");
            }
            user.setCreateBy(user.getUserName());
            SysDept dept = deptService.getDeptIdByAreaId(user.getAreaId());
            if (StringUtils.isNotNull(dept)) {
                user.setTenantId(dept.getTenantId());
                user.setDeptId(dept.getDeptId());//初始化为该行政区划部门
            }
            if (StringUtils.isEmpty(user.getPassword())) {
                String password = redisCache.getCacheObject(Constants.SYS_CONFIG_KEY + "sys.user.initPassword");
                if (StringUtils.isEmpty(password)) {
                    password = "123456";
                }
                user.setPassword(SecurityUtils.encryptPassword(password));
            } else {
                if (!user.isEncodePwd()) {
                    user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
                }
            }
            String tourist = redisCache.getCacheObject(Constants.SYS_CONFIG_KEY + "sys.tourist.role_" + user.getTenantId());
            if (StringUtils.isEmpty(tourist)) {
                tourist = "0";
            }
            Long[] roleIds = new Long[1];
            roleIds[0] = Long.valueOf(tourist);
            user.setRoleIds(roleIds);//初始化为游客身份
            user.setUserId(null);
            res = userService.insertUser(user, false);
        } else {
            if (StringUtils.isNotNull(dataUser.getAreaId()) && !dataUser.getAreaId().equals(user.getAreaId())) {//如果修改了租户
                SysDept dept = deptService.getDeptIdByAreaId(user.getAreaId());
                if (StringUtils.isNotNull(dept)) {
                    user.setDeptId(dept.getDeptId());//初始化为该行政区划部门
                    user.setTenantId(dept.getTenantId());
                }
            }
            user.setUserId(dataUser.getUserId());
            if ("2".equals(user.getDelFlag())) {
                res = userService.deleteUserById(dataUser.getUserId(), false);
            } else {
                if (StringUtils.isNotEmpty(user.getPhonenumber())
                        && UserConstants.NOT_UNIQUE.equals(userService.checkPhoneUnique(user))) {
                    return AjaxResult.error("修改用户'" + user.getUserName() + "'失败，手机号码已存在");
                }
                user.setUserName(user.getUserName());
                if (StringUtils.isNotEmpty(user.getPassword())) {
                    if (!user.isEncodePwd()) {
                        user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
                    }
                }
                res = userService.updateUser(user, false);
            }
        }
        return res > 0 ? AjaxResult.success("成功!") : AjaxResult.error("失败!");
    }

    @SneakyThrows
    @PostMapping("/logOut")
    public AjaxResult<String> logOut(@RequestBody ForeignParam param) {
        checkCanVisit(param);
        byte[] desData = Base64.decode(param.getData());
        byte[] desByte = SecurityDesUtil.tripleDESDecrypt(desData, KeysConstants.RSA_SECRET);
        JSONObject tokenJson = JSONObject.parseObject(new String(desByte, StandardCharsets.UTF_8));
        String sessionId = tokenJson.getString("allSession");
        XxlSsoUser user = SsoTokenLoginHelper.loginCheck(sessionId);
        if (StringUtils.isNotNull(user)) {//删除子系统令牌
            String tokenKey = redisCache.getCacheObject("xxl_map_" + user.getUserid() + "_capital_key");
            if (StringUtils.isNotEmpty(tokenKey)) {
                redisCache.deleteObject(tokenKey);
                redisCache.deleteObject("xxl_map_" + user.getUserid() + "_capital_key");
            }
        }
        return AjaxResult.success("成功!");
    }


    @SneakyThrows
    private void checkCanVisit(ForeignParam param) {
        if (StringUtils.isEmpty(param.getData()) || StringUtils.isEmpty(param.getSign())
                || StringUtils.isNull(param.getTimeStamp())) {
            throw new CustomException("无权访问!", 403);
        }
        byte[] desData = Base64.decode(param.getData());
        byte[] desByte = SecurityDesUtil.tripleDESDecrypt(desData, KeysConstants.RSA_SECRET);
        byte[] signContent = (new String(desByte, StandardCharsets.UTF_8) + param.getTimeStamp()).getBytes(StandardCharsets.UTF_8);
        byte[] signByte = Base64.decode(param.getSign());
        if (!SecurityDesUtil.verifyMethod(signContent, signByte, KeysConstants.PUB_KEY)) {
            throw new CustomException("签名校验失败!");
        }
        if (DateUtils.getMinutesPoor(param.getTimeStamp()) > 5) {
            throw new CustomException("签名已过期!");
        }
    }
}
