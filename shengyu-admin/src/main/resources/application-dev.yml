# 项目相关配置
shengyu:
  # 实例演示开关
  demoEnabled: true
  # 文件路径 示例（ Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: dev
  # 文档word转换静态html的路径
  learningHtmlPath: D:/ruoyi/learningHtml
  smsProxyUrl: none
server:
  # 服务器的HTTP端口，默认为8080
  port: 8091
# 日志配置
logging:
  level:
    com.shengyu: debug
    org.springframework: warn
    com.shengyu.framework.config.MybatisPlusConfig: warn
spring:
  # redis 配置
  redis:
    # 地址
    host: 127.0.0.1
    # 端口，默认为6379
    port: 16379
    # 数据库索引
    database: 0
    # 密码
    password: ''
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    druid:
      # 主库数据源
      master:
        # url: ************************************************************************************************************************************************************************
        #username: syxx
        #password: hnsy147.
        url: *******************************************************************************************************************************************************************
        username: root
        password:
      # 初始连接数
      initialSize: 50
      # 最小连接池数量
      minIdle: 100
      # 最大连接池数量
      maxActive: 200
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000
      # 配置检测连接是否有效
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: false
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username: admin
        login-password: HNsyadmin!8.
      filter:
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 3000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
xxl:
  # xxl统一认证中心配置
  sso:
    server: http://127.0.0.1:8090
    logout:
      path: /logout
    exclude:
      path: /login,logOut,/captchaImage
    redis:
      address: redis://127.0.0.1:16379
      expire:
        minute: 1440
baidu:
  clientId: t1Td9j1YxHYcV3vIerLopMEA
  clientSecret: W5g5VF5AJm0yvHXBNeRVjB6SmQQNZcBh
